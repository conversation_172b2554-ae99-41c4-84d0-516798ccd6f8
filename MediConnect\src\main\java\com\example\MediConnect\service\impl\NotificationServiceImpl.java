package com.example.MediConnect.service.impl;

import com.example.MediConnect.entity.Appointment;
import com.example.MediConnect.entity.User;
import com.example.MediConnect.service.NotificationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.mail.SimpleMailMessage;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.stereotype.Service;

import java.time.format.DateTimeFormatter;

@Service
public class NotificationServiceImpl implements NotificationService {
    
    private static final Logger logger = LoggerFactory.getLogger(NotificationServiceImpl.class);
    
    @Autowired(required = false)
    private JavaMailSender mailSender;
    
    @Value("${spring.mail.username:<EMAIL>}")
    private String fromEmail;
    
    @Value("${app.notification.email.enabled:false}")
    private boolean emailEnabled;
    
    private final DateTimeFormatter dateFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm");
    
    @Override
    public void sendAppointmentConfirmation(Appointment appointment) {
        try {
            String subject = "Appointment Confirmation - MediConnect";
            String message = buildAppointmentConfirmationMessage(appointment);
            
            // Send to patient
            sendEmail(appointment.getPatient().getEmail(), subject, message);
            
            // Send to doctor
            sendEmail(appointment.getDoctor().getEmail(), subject, message);
            
            logger.info("Appointment confirmation sent for appointment ID: {}", appointment.getId());
        } catch (Exception e) {
            logger.error("Failed to send appointment confirmation for appointment ID: {}", appointment.getId(), e);
        }
    }
    
    @Override
    public void sendAppointmentReminder(Appointment appointment) {
        try {
            String subject = "Appointment Reminder - MediConnect";
            String message = buildAppointmentReminderMessage(appointment);
            
            // Send to patient
            sendEmail(appointment.getPatient().getEmail(), subject, message);
            
            logger.info("Appointment reminder sent for appointment ID: {}", appointment.getId());
        } catch (Exception e) {
            logger.error("Failed to send appointment reminder for appointment ID: {}", appointment.getId(), e);
        }
    }
    
    @Override
    public void sendAppointmentCancellation(Appointment appointment) {
        try {
            String subject = "Appointment Cancellation - MediConnect";
            String message = buildAppointmentCancellationMessage(appointment);
            
            // Send to patient
            sendEmail(appointment.getPatient().getEmail(), subject, message);
            
            // Send to doctor
            sendEmail(appointment.getDoctor().getEmail(), subject, message);
            
            logger.info("Appointment cancellation sent for appointment ID: {}", appointment.getId());
        } catch (Exception e) {
            logger.error("Failed to send appointment cancellation for appointment ID: {}", appointment.getId(), e);
        }
    }
    
    @Override
    public void sendApprovalNotification(User user, boolean approved) {
        try {
            String subject = approved ? "Account Approved - MediConnect" : "Account Rejected - MediConnect";
            String message = buildApprovalMessage(user, approved);
            
            sendEmail(user.getEmail(), subject, message);
            
            logger.info("Approval notification sent to user: {} - Status: {}", user.getEmail(), approved ? "Approved" : "Rejected");
        } catch (Exception e) {
            logger.error("Failed to send approval notification to user: {}", user.getEmail(), e);
        }
    }
    
    @Override
    public void sendWelcomeEmail(User user) {
        try {
            String subject = "Welcome to MediConnect";
            String message = buildWelcomeMessage(user);
            
            sendEmail(user.getEmail(), subject, message);
            
            logger.info("Welcome email sent to user: {}", user.getEmail());
        } catch (Exception e) {
            logger.error("Failed to send welcome email to user: {}", user.getEmail(), e);
        }
    }
    
    @Override
    public void sendPasswordResetEmail(User user, String resetToken) {
        try {
            String subject = "Password Reset - MediConnect";
            String message = buildPasswordResetMessage(user, resetToken);
            
            sendEmail(user.getEmail(), subject, message);
            
            logger.info("Password reset email sent to user: {}", user.getEmail());
        } catch (Exception e) {
            logger.error("Failed to send password reset email to user: {}", user.getEmail(), e);
        }
    }
    
    private void sendEmail(String to, String subject, String text) {
        if (!emailEnabled || mailSender == null) {
            logger.info("Email notification (disabled): To: {}, Subject: {}", to, subject);
            return;
        }
        
        try {
            SimpleMailMessage message = new SimpleMailMessage();
            message.setFrom(fromEmail);
            message.setTo(to);
            message.setSubject(subject);
            message.setText(text);
            
            mailSender.send(message);
            logger.debug("Email sent successfully to: {}", to);
        } catch (Exception e) {
            logger.error("Failed to send email to: {}", to, e);
        }
    }
    
    private String buildAppointmentConfirmationMessage(Appointment appointment) {
        return String.format(
            "Dear %s,\n\n" +
            "Your appointment has been confirmed with the following details:\n\n" +
            "Doctor: Dr. %s\n" +
            "Clinic: %s\n" +
            "Date & Time: %s\n" +
            "Reason: %s\n\n" +
            "Please arrive 15 minutes before your scheduled time.\n\n" +
            "If you need to reschedule or cancel, please contact us at least 24 hours in advance.\n\n" +
            "Best regards,\n" +
            "MediConnect Team",
            appointment.getPatient().getName(),
            appointment.getDoctor().getName(),
            appointment.getClinic().getClinicName(),
            appointment.getAppointmentDate().format(dateFormatter),
            appointment.getReason() != null ? appointment.getReason() : "General consultation"
        );
    }
    
    private String buildAppointmentReminderMessage(Appointment appointment) {
        return String.format(
            "Dear %s,\n\n" +
            "This is a reminder for your upcoming appointment:\n\n" +
            "Doctor: Dr. %s\n" +
            "Clinic: %s\n" +
            "Date & Time: %s\n" +
            "Reason: %s\n\n" +
            "Please arrive 15 minutes before your scheduled time.\n\n" +
            "If you need to reschedule or cancel, please contact us immediately.\n\n" +
            "Best regards,\n" +
            "MediConnect Team",
            appointment.getPatient().getName(),
            appointment.getDoctor().getName(),
            appointment.getClinic().getClinicName(),
            appointment.getAppointmentDate().format(dateFormatter),
            appointment.getReason() != null ? appointment.getReason() : "General consultation"
        );
    }
    
    private String buildAppointmentCancellationMessage(Appointment appointment) {
        return String.format(
            "Dear %s,\n\n" +
            "Your appointment has been cancelled:\n\n" +
            "Doctor: Dr. %s\n" +
            "Clinic: %s\n" +
            "Date & Time: %s\n" +
            "Reason for cancellation: %s\n\n" +
            "If you would like to reschedule, please contact us or book a new appointment through our system.\n\n" +
            "Best regards,\n" +
            "MediConnect Team",
            appointment.getPatient().getName(),
            appointment.getDoctor().getName(),
            appointment.getClinic().getClinicName(),
            appointment.getAppointmentDate().format(dateFormatter),
            appointment.getCancellationReason() != null ? appointment.getCancellationReason() : "Not specified"
        );
    }
    
    private String buildApprovalMessage(User user, boolean approved) {
        if (approved) {
            return String.format(
                "Dear %s,\n\n" +
                "Congratulations! Your account has been approved.\n\n" +
                "You can now log in to MediConnect and access all features available for your role: %s\n\n" +
                "If you have any questions, please don't hesitate to contact our support team.\n\n" +
                "Best regards,\n" +
                "MediConnect Team",
                user.getName(),
                user.getRole().toString()
            );
        } else {
            return String.format(
                "Dear %s,\n\n" +
                "We regret to inform you that your account application has been rejected.\n\n" +
                "If you believe this is an error or would like more information, please contact our support team.\n\n" +
                "Best regards,\n" +
                "MediConnect Team",
                user.getName()
            );
        }
    }
    
    private String buildWelcomeMessage(User user) {
        return String.format(
            "Dear %s,\n\n" +
            "Welcome to MediConnect!\n\n" +
            "Your account has been successfully created with the role: %s\n\n" +
            "You can now log in using your email address and start using our healthcare management system.\n\n" +
            "If you need any assistance, please don't hesitate to contact our support team.\n\n" +
            "Best regards,\n" +
            "MediConnect Team",
            user.getName(),
            user.getRole().toString()
        );
    }
    
    private String buildPasswordResetMessage(User user, String resetToken) {
        return String.format(
            "Dear %s,\n\n" +
            "You have requested a password reset for your MediConnect account.\n\n" +
            "Your password reset token is: %s\n\n" +
            "This token will expire in 24 hours. If you did not request this reset, please ignore this email.\n\n" +
            "For security reasons, please do not share this token with anyone.\n\n" +
            "Best regards,\n" +
            "MediConnect Team",
            user.getName(),
            resetToken
        );
    }
}
