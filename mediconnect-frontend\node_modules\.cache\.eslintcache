[{"C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\reportWebVitals.js": "3", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\utils\\constants.js": "4", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\Dashboard.jsx": "5", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\context\\AuthContext.jsx": "6", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\auth\\Login.jsx": "7", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\layout\\ProtectedRoute.jsx": "8", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\services\\authService.js": "9", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\services\\api.js": "10", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\doctor\\DoctorDashboard.jsx": "11", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\admin\\AdminDashboard.jsx": "12", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\patient\\PatientDashboard.jsx": "13", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\clinic\\ClinicDashboard.jsx": "14", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\common\\Input.jsx": "15", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\common\\Button.jsx": "16", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\common\\Card.jsx": "17", "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\services\\mockData.js": "18"}, {"size": 535, "mtime": 1748259425122, "results": "19", "hashOfConfig": "20"}, {"size": 1163, "mtime": 1748262814010, "results": "21", "hashOfConfig": "20"}, {"size": 362, "mtime": 1748259425389, "results": "22", "hashOfConfig": "20"}, {"size": 4308, "mtime": 1748263171233, "results": "23", "hashOfConfig": "20"}, {"size": 1197, "mtime": **********790, "results": "24", "hashOfConfig": "20"}, {"size": 5267, "mtime": 1748262726054, "results": "25", "hashOfConfig": "20"}, {"size": 5732, "mtime": 1748262800126, "results": "26", "hashOfConfig": "20"}, {"size": 1112, "mtime": 1748262832389, "results": "27", "hashOfConfig": "20"}, {"size": 6585, "mtime": 1748263184055, "results": "28", "hashOfConfig": "20"}, {"size": 2464, "mtime": 1748262671772, "results": "29", "hashOfConfig": "20"}, {"size": 6913, "mtime": 1748262911758, "results": "30", "hashOfConfig": "20"}, {"size": 7495, "mtime": 1748263160204, "results": "31", "hashOfConfig": "20"}, {"size": 9116, "mtime": 1748262983817, "results": "32", "hashOfConfig": "20"}, {"size": 8411, "mtime": 1748262946259, "results": "33", "hashOfConfig": "20"}, {"size": 2586, "mtime": 1748262759271, "results": "34", "hashOfConfig": "20"}, {"size": 2136, "mtime": 1748262743765, "results": "35", "hashOfConfig": "20"}, {"size": 2081, "mtime": 1748262773798, "results": "36", "hashOfConfig": "20"}, {"size": 8517, "mtime": 1748262654453, "results": "37", "hashOfConfig": "20"}, {"filePath": "38", "messages": "39", "suppressedMessages": "40", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "pmkomv", {"filePath": "41", "messages": "42", "suppressedMessages": "43", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "44", "messages": "45", "suppressedMessages": "46", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "47", "messages": "48", "suppressedMessages": "49", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\utils\\constants.js", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\Dashboard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\context\\AuthContext.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\auth\\Login.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\layout\\ProtectedRoute.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\services\\authService.js", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\services\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\doctor\\DoctorDashboard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\admin\\AdminDashboard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\patient\\PatientDashboard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\clinic\\ClinicDashboard.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\common\\Input.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\common\\Button.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\components\\common\\Card.jsx", [], [], "C:\\Users\\<USER>\\OneDrive\\desktop\\MediConnec_Project\\mediconnect-frontend\\src\\services\\mockData.js", [], []]