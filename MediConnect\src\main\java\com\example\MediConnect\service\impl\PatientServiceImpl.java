package com.example.MediConnect.service.impl;

import com.example.MediConnect.dto.*;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.entity.*;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.*;
import com.example.MediConnect.service.AppointmentService;
import com.example.MediConnect.service.PatientService;
import com.example.MediConnect.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDate;
import java.util.List;
import java.util.stream.Collectors;

@Service
@Transactional
public class PatientServiceImpl implements PatientService {
    
    @Autowired
    private PatientRepository patientRepository;
    
    @Autowired
    private AppointmentService appointmentService;
    
    @Autowired
    private DoctorRepository doctorRepository;
    
    @Autowired
    private ClinicRepository clinicRepository;
    
    @Autowired
    private DiagnosisRepository diagnosisRepository;
    
    @Autowired
    private PrescriptionRepository prescriptionRepository;
    
    @Override
    public PatientDTO getPatientProfile(Long patientId) {
        Patient patient = patientRepository.findById(patientId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.PATIENT_NOT_FOUND));
        return convertToDTO(patient);
    }
    
    @Override
    public PatientDTO updatePatientProfile(Long patientId, PatientDTO patientDTO) {
        Patient patient = patientRepository.findById(patientId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.PATIENT_NOT_FOUND));
        
        patient.setName(patientDTO.getName());
        patient.setPhoneNumber(patientDTO.getPhoneNumber());
        patient.setAddress(patientDTO.getAddress());
        patient.setEmergencyContactName(patientDTO.getEmergencyContactName());
        patient.setEmergencyContactPhone(patientDTO.getEmergencyContactPhone());
        patient.setBloodGroup(patientDTO.getBloodGroup());
        patient.setAllergies(patientDTO.getAllergies());
        patient.setMedicalHistory(patientDTO.getMedicalHistory());
        patient.setInsuranceProvider(patientDTO.getInsuranceProvider());
        patient.setInsuranceNumber(patientDTO.getInsuranceNumber());
        
        patient = patientRepository.save(patient);
        return convertToDTO(patient);
    }
    
    @Override
    public AppointmentDTO bookAppointment(AppointmentRequest appointmentRequest) {
        return appointmentService.createAppointment(appointmentRequest);
    }
    
    @Override
    public List<AppointmentDTO> getPatientAppointments(Long patientId) {
        return appointmentService.getAppointmentsByPatient(patientId);
    }
    
    @Override
    public List<AppointmentDTO> getAppointmentHistory(Long patientId) {
        return appointmentService.getAppointmentsByPatient(patientId);
    }
    
    @Override
    public void cancelAppointment(Long appointmentId) {
        appointmentService.cancelAppointment(appointmentId, "Cancelled by patient");
    }
    
    @Override
    public List<DoctorDTO> searchDoctorsBySpecialty(Speciality speciality) {
        List<Doctor> doctors = doctorRepository.findBySpeciality(speciality);
        return doctors.stream()
                .map(this::convertToDoctorDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<ClinicDTO> searchClinicsBySpecialty(Speciality speciality) {
        // This is a simplified implementation
        // In a real application, you would search clinics that have doctors with the specified specialty
        List<Clinic> clinics = clinicRepository.findAll();
        return clinics.stream()
                .map(this::convertToClinicDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<String> getDoctorAvailability(Long doctorId, LocalDate date) {
        return appointmentService.getAvailableSlots(doctorId, date.toString());
    }
    
    @Override
    public List<DiagnosisDTO> getMedicalHistory(Long patientId) {
        List<Diagnosis> diagnoses = diagnosisRepository.findByPatientIdOrderByCreatedAtDesc(patientId);
        return diagnoses.stream()
                .map(this::convertToDiagnosisDTO)
                .collect(Collectors.toList());
    }
    
    @Override
    public List<DiagnosisDTO> getDiagnoses(Long patientId) {
        return getMedicalHistory(patientId);
    }
    
    @Override
    public List<PrescriptionDTO> getPrescriptions(Long patientId) {
        List<Prescription> prescriptions = prescriptionRepository.findByPatientIdOrderByCreatedAtDesc(patientId);
        return prescriptions.stream()
                .map(this::convertToPrescriptionDTO)
                .collect(Collectors.toList());
    }
    
    private PatientDTO convertToDTO(Patient patient) {
        PatientDTO dto = new PatientDTO();
        dto.setId(patient.getId());
        dto.setName(patient.getName());
        dto.setEmail(patient.getEmail());
        dto.setPhoneNumber(patient.getPhoneNumber());
        dto.setAddress(patient.getAddress());
        dto.setDateOfBirth(patient.getDateOfBirth());
        dto.setGender(patient.getGender());
        dto.setPatientId(patient.getPatientId());
        dto.setEmergencyContactName(patient.getEmergencyContactName());
        dto.setEmergencyContactPhone(patient.getEmergencyContactPhone());
        dto.setBloodGroup(patient.getBloodGroup());
        dto.setAllergies(patient.getAllergies());
        dto.setMedicalHistory(patient.getMedicalHistory());
        dto.setInsuranceProvider(patient.getInsuranceProvider());
        dto.setInsuranceNumber(patient.getInsuranceNumber());
        dto.setCreatedAt(patient.getCreatedAt());
        dto.setUpdatedAt(patient.getUpdatedAt());
        return dto;
    }
    
    private DoctorDTO convertToDoctorDTO(Doctor doctor) {
        DoctorDTO dto = new DoctorDTO();
        dto.setId(doctor.getId());
        dto.setName(doctor.getName());
        dto.setEmail(doctor.getEmail());
        dto.setPhoneNumber(doctor.getPhoneNumber());
        dto.setMedicalLicense(doctor.getMedicalLicense());
        dto.setYearsOfExperience(doctor.getYearsOfExperience());
        dto.setQualification(doctor.getQualification());
        dto.setDoctorStatus(doctor.getDoctorStatus());
        dto.setConsultationFee(doctor.getConsultationFee());
        dto.setBio(doctor.getBio());
        
        if (doctor.getSpeciality() != null) {
            dto.setSpecialtyName(doctor.getSpeciality().getName().toString());
        }
        
        if (doctor.getClinic() != null) {
            dto.setClinicId(doctor.getClinic().getId());
            dto.setClinicName(doctor.getClinic().getClinicName());
        }
        
        return dto;
    }
    
    private ClinicDTO convertToClinicDTO(Clinic clinic) {
        ClinicDTO dto = new ClinicDTO();
        dto.setId(clinic.getId());
        dto.setName(clinic.getName());
        dto.setEmail(clinic.getEmail());
        dto.setPhoneNumber(clinic.getPhoneNumber());
        dto.setAddress(clinic.getAddress());
        dto.setClinicName(clinic.getClinicName());
        dto.setDescription(clinic.getDescription());
        dto.setOperatingHours(clinic.getOperatingHours());
        dto.setEmergencyContact(clinic.getEmergencyContact());
        dto.setWebsiteUrl(clinic.getWebsiteUrl());
        return dto;
    }
    
    private DiagnosisDTO convertToDiagnosisDTO(Diagnosis diagnosis) {
        DiagnosisDTO dto = new DiagnosisDTO();
        dto.setId(diagnosis.getId());
        dto.setDiagnosisCode(diagnosis.getDiagnosisCode());
        dto.setDiagnosisDescription(diagnosis.getDiagnosisDescription());
        dto.setSymptoms(diagnosis.getSymptoms());
        dto.setTreatmentPlan(diagnosis.getTreatmentPlan());
        dto.setDoctorNotes(diagnosis.getDoctorNotes());
        dto.setCreatedAt(diagnosis.getCreatedAt());
        
        if (diagnosis.getPatient() != null) {
            dto.setPatientId(diagnosis.getPatient().getId());
            dto.setPatientName(diagnosis.getPatient().getName());
        }
        
        if (diagnosis.getDoctor() != null) {
            dto.setDoctorId(diagnosis.getDoctor().getId());
            dto.setDoctorName(diagnosis.getDoctor().getName());
        }
        
        return dto;
    }
    
    private PrescriptionDTO convertToPrescriptionDTO(Prescription prescription) {
        PrescriptionDTO dto = new PrescriptionDTO();
        dto.setId(prescription.getId());
        dto.setDiagnosisId(prescription.getDiagnosis().getId());
        dto.setMedicationName(prescription.getMedicationName());
        dto.setDosage(prescription.getDosage());
        dto.setFrequency(prescription.getFrequency());
        dto.setDuration(prescription.getDuration());
        dto.setInstructions(prescription.getInstructions());
        dto.setQuantity(prescription.getQuantity());
        dto.setRefills(prescription.getRefills());
        dto.setCreatedAt(prescription.getCreatedAt());
        dto.setIsActive(prescription.getIsActive());
        return dto;
    }
}
