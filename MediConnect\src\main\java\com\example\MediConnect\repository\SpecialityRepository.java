package com.example.MediConnect.repository;

import com.example.MediConnect.entity.Speciality;
import com.example.MediConnect.enums.Specialization;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface SpecialityRepository extends JpaRepository<Speciality, Long> {
    
    Optional<Speciality> findByName(Specialization name);
    
    List<Speciality> findByIsActive(Boolean isActive);
    
    boolean existsByName(Specialization name);
}
