package com.example.MediConnect.controller;

import com.example.MediConnect.dto.ClinicDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.response.ApiResponse;
import com.example.MediConnect.dto.response.SystemReportResponse;
import com.example.MediConnect.entity.User;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.service.AdminService;
import com.example.MediConnect.util.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/admin")
@CrossOrigin(origins = "*")
@PreAuthorize("hasRole('ADMIN')")
public class AdminController {

    @Autowired
    private AdminService adminService;

    // Approval Management
    @GetMapping("/clinics/pending")
    public ResponseEntity<List<ClinicDTO>> getPendingClinicApprovals() {
        List<ClinicDTO> pendingClinics = adminService.getPendingClinicApprovals();
        return ResponseEntity.ok(pendingClinics);
    }

    @PutMapping("/clinics/{id}/approve")
    public ResponseEntity<ApiResponse> approveClinic(@PathVariable Long id) {
        adminService.approveClinic(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.CLINIC_APPROVED_SUCCESSFULLY));
    }

    @PutMapping("/clinics/{id}/reject")
    public ResponseEntity<ApiResponse> rejectClinic(@PathVariable Long id) {
        adminService.rejectClinic(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.CLINIC_REJECTED_SUCCESSFULLY));
    }

    @GetMapping("/doctors/pending")
    public ResponseEntity<List<DoctorDTO>> getPendingDoctorApprovals() {
        List<DoctorDTO> pendingDoctors = adminService.getPendingDoctorApprovals();
        return ResponseEntity.ok(pendingDoctors);
    }

    @PutMapping("/doctors/{id}/approve")
    public ResponseEntity<ApiResponse> approveDoctor(@PathVariable Long id) {
        adminService.approveDoctor(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.DOCTOR_APPROVED_SUCCESSFULLY));
    }

    @PutMapping("/doctors/{id}/reject")
    public ResponseEntity<ApiResponse> rejectDoctor(@PathVariable Long id) {
        adminService.rejectDoctor(id);
        return ResponseEntity.ok(new ApiResponse(true, Constants.DOCTOR_REJECTED_SUCCESSFULLY));
    }

    // User Management
    @GetMapping("/users")
    public ResponseEntity<List<User>> getAllUsers() {
        List<User> users = adminService.getAllUsers();
        return ResponseEntity.ok(users);
    }

    @GetMapping("/users/{id}")
    public ResponseEntity<User> getUserById(@PathVariable Long id) {
        User user = adminService.getUserById(id);
        return ResponseEntity.ok(user);
    }

    @PutMapping("/users/{id}/activate")
    public ResponseEntity<ApiResponse> activateUser(@PathVariable Long id) {
        adminService.activateUser(id);
        return ResponseEntity.ok(new ApiResponse(true, "User activated successfully"));
    }

    @PutMapping("/users/{id}/deactivate")
    public ResponseEntity<ApiResponse> deactivateUser(@PathVariable Long id) {
        adminService.deactivateUser(id);
        return ResponseEntity.ok(new ApiResponse(true, "User deactivated successfully"));
    }

    @GetMapping("/users/status/{status}")
    public ResponseEntity<List<User>> getUsersByStatus(@PathVariable UserStatus status) {
        List<User> users = adminService.getUsersByStatus(status);
        return ResponseEntity.ok(users);
    }

    // System Reports
    @GetMapping("/reports/overview")
    public ResponseEntity<SystemReportResponse> getSystemOverview() {
        SystemReportResponse report = adminService.getSystemOverview();
        return ResponseEntity.ok(report);
    }

    @GetMapping("/reports/users")
    public ResponseEntity<SystemReportResponse> getUserStatistics() {
        SystemReportResponse report = adminService.getUserStatistics();
        return ResponseEntity.ok(report);
    }

    @GetMapping("/reports/appointments")
    public ResponseEntity<SystemReportResponse> getAppointmentStatistics() {
        SystemReportResponse report = adminService.getAppointmentStatistics();
        return ResponseEntity.ok(report);
    }

    // Initialize Sample Data (for testing purposes)
    @PostMapping("/initialize-sample-data")
    public ResponseEntity<ApiResponse> initializeSampleData() {
        try {
            adminService.initializeSampleData();
            return ResponseEntity.ok(new ApiResponse(true, "Sample data initialized successfully"));
        } catch (Exception e) {
            return ResponseEntity.ok(new ApiResponse(false, "Failed to initialize sample data: " + e.getMessage()));
        }
    }
}
