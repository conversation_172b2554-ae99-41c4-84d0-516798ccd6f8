package com.example.MediConnect.service;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.CommandLineRunner;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.example.MediConnect.entity.Admin;
import com.example.MediConnect.entity.Announcement;
import com.example.MediConnect.entity.Appointment;
import com.example.MediConnect.entity.Clinic;
import com.example.MediConnect.entity.Diagnosis;
import com.example.MediConnect.entity.Doctor;
import com.example.MediConnect.entity.FollowUp;
import com.example.MediConnect.entity.Patient;
import com.example.MediConnect.entity.Prescription;
import com.example.MediConnect.entity.Speciality;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.enums.ClinicStatus;
import com.example.MediConnect.enums.DoctorStatus;
import com.example.MediConnect.enums.FollowUpStatus;
import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.Specialization;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.repository.AdminRepository;
import com.example.MediConnect.repository.AnnouncementRepository;
import com.example.MediConnect.repository.AppointmentRepository;
import com.example.MediConnect.repository.ClinicRepository;
import com.example.MediConnect.repository.DiagnosisRepository;
import com.example.MediConnect.repository.DoctorRepository;
import com.example.MediConnect.repository.FollowUpRepository;
import com.example.MediConnect.repository.PatientRepository;
import com.example.MediConnect.repository.PrescriptionRepository;
import com.example.MediConnect.repository.SpecialityRepository;
import com.example.MediConnect.repository.UserRepository;

@Service
@Transactional
public class DataInitializationService implements CommandLineRunner {

    private static final Logger logger = LoggerFactory.getLogger(DataInitializationService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private AdminRepository adminRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    @Autowired
    private ClinicRepository clinicRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private SpecialityRepository specialityRepository;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private DiagnosisRepository diagnosisRepository;

    @Autowired
    private PrescriptionRepository prescriptionRepository;

    @Autowired
    private FollowUpRepository followUpRepository;

    @Autowired
    private AnnouncementRepository announcementRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Override
    public void run(String... args) throws Exception {
        if (userRepository.count() == 0) {
            logger.info("Initializing sample data...");
            initializeSampleData();
            logger.info("Sample data initialization completed!");
        } else {
            logger.info("Sample data already exists, skipping initialization.");
        }
    }

    public void forceInitializeSampleData() throws Exception {
        logger.info("Force initializing sample data...");
        initializeSampleData();
        logger.info("Sample data force initialization completed!");
    }

    private void initializeSampleData() {
        // Create Specialities
        List<Speciality> specialities = createSpecialities();

        // Create Admin
        Admin admin = createAdmin();

        // Create Clinics
        List<Clinic> clinics = createClinics();

        // Create Doctors
        List<Doctor> doctors = createDoctors(specialities, clinics);

        // Create Patients
        List<Patient> patients = createPatients();

        // Create Appointments
        List<Appointment> appointments = createAppointments(doctors, patients, clinics);

        // Create Diagnoses and Prescriptions
        createDiagnosesAndPrescriptions(doctors, patients, appointments);

        // Create Follow-ups
        createFollowUps(doctors, patients);

        // Create Announcements
        createAnnouncements(clinics);
    }

    private List<Speciality> createSpecialities() {
        List<Speciality> specialities = Arrays.asList(
            createSpeciality(Specialization.CARDIOLOGY, "Heart and cardiovascular system specialist"),
            createSpeciality(Specialization.DERMATOLOGY, "Skin, hair, and nail specialist"),
            createSpeciality(Specialization.NEUROLOGY, "Brain and nervous system specialist"),
            createSpeciality(Specialization.ORTHOPEDICS, "Bone, joint, and muscle specialist"),
            createSpeciality(Specialization.PEDIATRICS, "Children's health specialist"),
            createSpeciality(Specialization.PSYCHIATRY, "Mental health specialist"),
            createSpeciality(Specialization.GENERAL_MEDICINE, "General practice and family medicine")
        );

        return specialityRepository.saveAll(specialities);
    }

    private Speciality createSpeciality(Specialization name, String description) {
        Speciality speciality = new Speciality();
        speciality.setName(name);
        speciality.setDescription(description);
        return speciality;
    }

    private Admin createAdmin() {
        Admin admin = new Admin();
        admin.setName("System Administrator");
        admin.setEmail("<EMAIL>");
        admin.setPassword(passwordEncoder.encode("admin123"));
        admin.setPhoneNumber("******-0001");
        admin.setAddress("123 Admin Street, Medical City, MC 12345");
        admin.setDateOfBirth("1980-01-15");
        admin.setGender("MALE");
        admin.setRole(Role.ADMIN);
        admin.setStatus(UserStatus.ACTIVE);
        admin.setEmployeeId("ADM001");
        admin.setDepartment("IT Administration");
        admin.setAccessLevel("SUPER_ADMIN");

        return adminRepository.save(admin);
    }

    private List<Clinic> createClinics() {
        List<Clinic> clinics = Arrays.asList(
            createClinic("MediConnect Central Clinic", "<EMAIL>", "******-0100",
                        "456 Central Avenue, Medical District, MC 12346",
                        "Premier healthcare facility with state-of-the-art equipment",
                        "Mon-Fri: 8:00 AM - 8:00 PM, Sat-Sun: 9:00 AM - 5:00 PM",
                        "******-0199", "https://central.mediconnect.com"),

            createClinic("MediConnect North Branch", "<EMAIL>", "******-0200",
                        "789 North Street, Uptown, MC 12347",
                        "Specialized in family medicine and pediatric care",
                        "Mon-Fri: 7:00 AM - 7:00 PM, Sat: 8:00 AM - 4:00 PM",
                        "******-0299", "https://north.mediconnect.com"),

            createClinic("MediConnect Specialty Center", "<EMAIL>", "******-0300",
                        "321 Specialty Boulevard, Medical Plaza, MC 12348",
                        "Advanced specialty care and surgical procedures",
                        "Mon-Fri: 6:00 AM - 10:00 PM, Sat-Sun: 8:00 AM - 6:00 PM",
                        "******-0399", "https://specialty.mediconnect.com")
        );

        return clinicRepository.saveAll(clinics);
    }

    private Clinic createClinic(String name, String email, String phone, String address,
                               String description, String hours, String emergency, String website) {
        Clinic clinic = new Clinic();
        clinic.setName(name);
        clinic.setEmail(email);
        clinic.setPassword(passwordEncoder.encode("clinic123"));
        clinic.setPhoneNumber(phone);
        clinic.setAddress(address);
        clinic.setDateOfBirth("2010-01-01"); // Establishment date
        clinic.setGender("ORGANIZATION");
        clinic.setRole(Role.CLINIC);
        clinic.setStatus(UserStatus.ACTIVE);
        clinic.setClinicName(name);
        clinic.setDescription(description);
        clinic.setClinicStatus(ClinicStatus.ACTIVE);
        clinic.setOperatingHours(hours);
        clinic.setEmergencyContact(emergency);
        clinic.setWebsiteUrl(website);

        return clinic;
    }

    private List<Doctor> createDoctors(List<Speciality> specialities, List<Clinic> clinics) {
        List<Doctor> doctors = Arrays.asList(
            createDoctor("Dr. Sarah Johnson", "<EMAIL>", "******-1001",
                        "1985-03-20", "FEMALE", "MD12345", 12, "MBBS, MD Cardiology",
                        150.0, "Experienced cardiologist specializing in heart disease prevention",
                        specialities.get(0), clinics.get(0)), // Cardiology at Central

            createDoctor("Dr. Michael Chen", "<EMAIL>", "******-1002",
                        "1982-07-15", "MALE", "MD12346", 15, "MBBS, MD Dermatology",
                        120.0, "Dermatologist with expertise in skin cancer treatment",
                        specialities.get(1), clinics.get(0)), // Dermatology at Central

            createDoctor("Dr. Emily Rodriguez", "<EMAIL>", "******-1003",
                        "1988-11-08", "FEMALE", "MD12347", 8, "MBBS, MD Pediatrics",
                        130.0, "Pediatrician dedicated to children's health and development",
                        specialities.get(4), clinics.get(1)), // Pediatrics at North

            createDoctor("Dr. James Wilson", "<EMAIL>", "******-1004",
                        "1979-05-12", "MALE", "MD12348", 18, "MBBS, MS Orthopedics",
                        180.0, "Orthopedic surgeon specializing in joint replacement",
                        specialities.get(3), clinics.get(2)), // Orthopedics at Specialty

            createDoctor("Dr. Lisa Thompson", "<EMAIL>", "******-1005",
                        "1986-09-25", "FEMALE", "MD12349", 10, "MBBS, MD Neurology",
                        160.0, "Neurologist with focus on stroke and epilepsy treatment",
                        specialities.get(2), clinics.get(2)) // Neurology at Specialty
        );

        return doctorRepository.saveAll(doctors);
    }

    private Doctor createDoctor(String name, String email, String phone, String dob, String gender,
                               String license, int experience, String qualification, double fee,
                               String bio, Speciality speciality, Clinic clinic) {
        Doctor doctor = new Doctor();
        doctor.setName(name);
        doctor.setEmail(email);
        doctor.setPassword(passwordEncoder.encode("doctor123"));
        doctor.setPhoneNumber(phone);
        doctor.setAddress("Medical District, MC");
        doctor.setDateOfBirth(dob);
        doctor.setGender(gender);
        doctor.setRole(Role.DOCTOR);
        doctor.setStatus(UserStatus.ACTIVE);
        doctor.setMedicalLicense(license);
        doctor.setYearsOfExperience(experience);
        doctor.setQualification(qualification);
        doctor.setDoctorStatus(DoctorStatus.ACTIVE);
        doctor.setConsultationFee(fee);
        doctor.setBio(bio);
        doctor.setSpeciality(speciality);
        doctor.setClinic(clinic);

        return doctor;
    }

    private List<Patient> createPatients() {
        List<Patient> patients = Arrays.asList(
            createPatient("John Smith", "<EMAIL>", "******-2001", "1990-04-15", "MALE",
                         "PAT001", "Jane Smith", "******-2002", "O+", "None", "No significant history"),

            createPatient("Mary Johnson", "<EMAIL>", "******-2003", "1985-08-22", "FEMALE",
                         "PAT002", "Robert Johnson", "******-2004", "A+", "Penicillin", "Hypertension"),

            createPatient("David Brown", "<EMAIL>", "******-2005", "1992-12-10", "MALE",
                         "PAT003", "Sarah Brown", "******-2006", "B+", "None", "Diabetes Type 2"),

            createPatient("Jennifer Davis", "<EMAIL>", "******-2007", "1988-06-30", "FEMALE",
                         "PAT004", "Michael Davis", "******-2008", "AB+", "Aspirin", "Asthma"),

            createPatient("Robert Wilson", "<EMAIL>", "******-2009", "1975-02-18", "MALE",
                         "PAT005", "Linda Wilson", "******-2010", "O-", "None", "Heart disease family history")
        );

        return patientRepository.saveAll(patients);
    }

    private Patient createPatient(String name, String email, String phone, String dob, String gender,
                                 String patientId, String emergencyName, String emergencyPhone,
                                 String bloodGroup, String allergies, String medicalHistory) {
        Patient patient = new Patient();
        patient.setName(name);
        patient.setEmail(email);
        patient.setPassword(passwordEncoder.encode("patient123"));
        patient.setPhoneNumber(phone);
        patient.setAddress("Patient District, MC");
        patient.setDateOfBirth(dob);
        patient.setGender(gender);
        patient.setRole(Role.PATIENT);
        patient.setStatus(UserStatus.ACTIVE);
        patient.setPatientId(patientId);
        patient.setEmergencyContactName(emergencyName);
        patient.setEmergencyContactPhone(emergencyPhone);
        patient.setBloodGroup(bloodGroup);
        patient.setAllergies(allergies);
        patient.setMedicalHistory(medicalHistory);

        return patient;
    }

    private List<Appointment> createAppointments(List<Doctor> doctors, List<Patient> patients, List<Clinic> clinics) {
        List<Appointment> appointments = Arrays.asList(
            createAppointment(patients.get(0), doctors.get(0), clinics.get(0),
                            LocalDateTime.now().plusDays(1).withHour(10).withMinute(0),
                            AppointmentStatus.SCHEDULED, "Regular checkup", 30),

            createAppointment(patients.get(1), doctors.get(1), clinics.get(0),
                            LocalDateTime.now().plusDays(2).withHour(14).withMinute(30),
                            AppointmentStatus.SCHEDULED, "Skin consultation", 45),

            createAppointment(patients.get(2), doctors.get(2), clinics.get(1),
                            LocalDateTime.now().plusDays(3).withHour(9).withMinute(15),
                            AppointmentStatus.SCHEDULED, "Child vaccination", 20),

            createAppointment(patients.get(3), doctors.get(3), clinics.get(2),
                            LocalDateTime.now().minusDays(1).withHour(11).withMinute(0),
                            AppointmentStatus.COMPLETED, "Joint pain evaluation", 60),

            createAppointment(patients.get(4), doctors.get(4), clinics.get(2),
                            LocalDateTime.now().plusDays(5).withHour(15).withMinute(45),
                            AppointmentStatus.SCHEDULED, "Neurological assessment", 45)
        );

        return appointmentRepository.saveAll(appointments);
    }

    private Appointment createAppointment(Patient patient, Doctor doctor, Clinic clinic,
                                        LocalDateTime dateTime, AppointmentStatus status,
                                        String reason, int duration) {
        Appointment appointment = new Appointment();
        appointment.setPatient(patient);
        appointment.setDoctor(doctor);
        appointment.setClinic(clinic);
        appointment.setAppointmentDate(dateTime);
        appointment.setStatus(status);
        appointment.setReason(reason);
        appointment.setDurationMinutes(duration);
        appointment.setNotes("Sample appointment created during initialization");

        return appointment;
    }

    private void createDiagnosesAndPrescriptions(List<Doctor> doctors, List<Patient> patients, List<Appointment> appointments) {
        // Create diagnosis for completed appointment
        Diagnosis diagnosis = new Diagnosis();
        diagnosis.setPatient(patients.get(3));
        diagnosis.setDoctor(doctors.get(3));
        diagnosis.setAppointment(appointments.get(3));
        diagnosis.setDiagnosisCode("M25.50");
        diagnosis.setDiagnosisDescription("Joint pain, unspecified joint");
        diagnosis.setSymptoms("Joint stiffness, pain during movement, swelling");
        diagnosis.setTreatmentPlan("Physical therapy, anti-inflammatory medication, follow-up in 2 weeks");
        diagnosis.setDoctorNotes("Patient shows signs of early arthritis. Recommend lifestyle changes.");

        diagnosis = diagnosisRepository.save(diagnosis);

        // Create prescriptions for the diagnosis
        List<Prescription> prescriptions = Arrays.asList(
            createPrescription(diagnosis, "Ibuprofen", "400mg", "Twice daily", "2 weeks",
                             "Take with food to avoid stomach upset", 28, 0),

            createPrescription(diagnosis, "Glucosamine", "1500mg", "Once daily", "3 months",
                             "Take with meals", 90, 2)
        );

        prescriptionRepository.saveAll(prescriptions);
    }

    private Prescription createPrescription(Diagnosis diagnosis, String medication, String dosage,
                                          String frequency, String duration, String instructions,
                                          int quantity, int refills) {
        Prescription prescription = new Prescription();
        prescription.setDiagnosis(diagnosis);
        prescription.setMedicationName(medication);
        prescription.setDosage(dosage);
        prescription.setFrequency(frequency);
        prescription.setDuration(duration);
        prescription.setInstructions(instructions);
        prescription.setQuantity(quantity);
        prescription.setRefills(refills);
        prescription.setIsActive(true);

        return prescription;
    }

    private void createFollowUps(List<Doctor> doctors, List<Patient> patients) {
        List<FollowUp> followUps = Arrays.asList(
            createFollowUp(patients.get(3), doctors.get(3),
                          LocalDateTime.now().plusDays(14).withHour(10).withMinute(0),
                          FollowUpStatus.SCHEDULED, "Joint pain reassessment",
                          "Check progress after medication and physical therapy"),

            createFollowUp(patients.get(1), doctors.get(1),
                          LocalDateTime.now().plusDays(30).withHour(14).withMinute(0),
                          FollowUpStatus.SCHEDULED, "Skin condition monitoring",
                          "Evaluate treatment effectiveness")
        );

        followUpRepository.saveAll(followUps);
    }

    private FollowUp createFollowUp(Patient patient, Doctor doctor, LocalDateTime scheduledDate,
                                   FollowUpStatus status, String reason, String notes) {
        FollowUp followUp = new FollowUp();
        followUp.setPatient(patient);
        followUp.setDoctor(doctor);
        followUp.setScheduledDate(scheduledDate);
        followUp.setStatus(status);
        followUp.setReason(reason);
        followUp.setNotes(notes);

        return followUp;
    }

    private void createAnnouncements(List<Clinic> clinics) {
        List<Announcement> announcements = Arrays.asList(
            createAnnouncement(clinics.get(0), "New COVID-19 Safety Protocols",
                             "We have implemented enhanced safety measures for all patients and staff.",
                             "HIGH", true,
                             LocalDateTime.now().minusDays(7),
                             LocalDateTime.now().plusDays(30)),

            createAnnouncement(clinics.get(1), "Extended Hours for Pediatric Care",
                             "Our pediatric department now offers extended hours on weekends.",
                             "MEDIUM", true,
                             LocalDateTime.now().minusDays(3),
                             LocalDateTime.now().plusDays(60)),

            createAnnouncement(clinics.get(2), "New Orthopedic Equipment",
                             "We have installed state-of-the-art orthopedic diagnostic equipment.",
                             "LOW", true,
                             LocalDateTime.now().minusDays(1),
                             LocalDateTime.now().plusDays(45))
        );

        announcementRepository.saveAll(announcements);
    }

    private Announcement createAnnouncement(Clinic clinic, String title, String content,
                                          String priority, boolean isActive,
                                          LocalDateTime startDate, LocalDateTime endDate) {
        Announcement announcement = new Announcement();
        announcement.setClinic(clinic);
        announcement.setTitle(title);
        announcement.setContent(content);
        announcement.setPriority(priority);
        announcement.setIsActive(isActive);
        announcement.setStartDate(startDate);
        announcement.setEndDate(endDate);

        return announcement;
    }
}
