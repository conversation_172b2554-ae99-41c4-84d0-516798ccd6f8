import { apiRequest, mockResponse, isMockMode } from './api';
import { STORAGE_KEYS } from '../utils/constants';

// Mock authentication data
const mockAuthData = {
  admin: {
    token: 'mock_admin_token_123',
    email: '<EMAIL>',
    role: 'ADMIN',
    userId: 1,
    name: 'Test Admin',
    message: 'Authentication successful'
  },
  doctor: {
    token: 'mock_doctor_token_456',
    email: '<EMAIL>',
    role: 'DOCTOR',
    userId: 2,
    name: 'Dr. <PERSON>',
    message: 'Authentication successful'
  },
  clinic: {
    token: 'mock_clinic_token_789',
    email: '<EMAIL>',
    role: 'CLINIC',
    userId: 3,
    name: 'MediConnect Central Clinic',
    message: 'Authentication successful'
  },
  patient: {
    token: 'mock_patient_token_101',
    email: '<EMAIL>',
    role: 'PATIENT',
    userId: 4,
    name: '<PERSON>',
    message: 'Authentication successful'
  }
};

// Authentication Service
export const authService = {
  // Login
  login: async (credentials) => {
    if (isMockMode()) {
      // Mock login logic
      const { email, password } = credentials;

      // Simple mock authentication
      let authData = null;
      if (email === '<EMAIL>' && password === 'test123') {
        authData = mockAuthData.admin;
      } else if (email === '<EMAIL>' && password === 'test123') {
        authData = mockAuthData.doctor;
      } else if (email === '<EMAIL>' && password === 'test123') {
        authData = mockAuthData.clinic;
      } else if (email === '<EMAIL>' && password === 'test123') {
        authData = mockAuthData.patient;
      }

      if (authData) {
        // Store token and user data
        localStorage.setItem(STORAGE_KEYS.TOKEN, authData.token);
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({
          id: authData.userId,
          name: authData.name,
          email: authData.email,
          role: authData.role
        }));

        return mockResponse(authData);
      } else {
        throw new Error('Invalid credentials');
      }
    } else {
      // Real API call
      const response = await apiRequest.post('/auth/login', credentials);

      // Store token and user data
      const { token, email, role, userId, name } = response.data;
      localStorage.setItem(STORAGE_KEYS.TOKEN, token);
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({
        id: userId,
        name,
        email,
        role
      }));

      return response;
    }
  },

  // Register
  register: async (userData) => {
    if (isMockMode()) {
      // Mock registration
      const newUser = {
        ...userData,
        id: Date.now(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };

      // Generate mock token based on role
      const tokenMap = {
        ADMIN: 'mock_admin_token_new',
        DOCTOR: 'mock_doctor_token_new',
        CLINIC: 'mock_clinic_token_new',
        PATIENT: 'mock_patient_token_new'
      };

      const authData = {
        token: tokenMap[userData.role] || 'mock_token_new',
        email: userData.email,
        role: userData.role,
        userId: newUser.id,
        name: userData.name,
        message: 'Registration successful'
      };

      // Store token and user data
      localStorage.setItem(STORAGE_KEYS.TOKEN, authData.token);
      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({
        id: authData.userId,
        name: authData.name,
        email: authData.email,
        role: authData.role
      }));

      return mockResponse(authData);
    } else {
      // Real API call
      const response = await apiRequest.post('/auth/register', userData);

      // Store token and user data if registration includes auto-login
      if (response.data.token) {
        const { token, email, role, userId, name } = response.data;
        localStorage.setItem(STORAGE_KEYS.TOKEN, token);
        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({
          id: userId,
          name,
          email,
          role
        }));
      }

      return response;
    }
  },

  // Logout
  logout: async () => {
    if (isMockMode()) {
      // Mock logout
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);
      return mockResponse({
        success: true,
        message: 'Logout successful',
        data: null
      });
    } else {
      // Real API call
      try {
        await apiRequest.post('/auth/logout');
      } catch (error) {
        // Continue with logout even if API call fails
        console.warn('Logout API call failed:', error);
      }

      // Clear local storage
      localStorage.removeItem(STORAGE_KEYS.TOKEN);
      localStorage.removeItem(STORAGE_KEYS.USER);

      return { data: { success: true, message: 'Logout successful' } };
    }
  },

  // Validate token
  validateToken: async () => {
    if (isMockMode()) {
      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
      if (token && token.startsWith('mock_')) {
        return mockResponse({
          success: true,
          message: 'Token is valid',
          data: null
        });
      } else {
        throw new Error('Invalid token');
      }
    } else {
      return apiRequest.post('/auth/validate');
    }
  },

  // Refresh token
  refreshToken: async () => {
    if (isMockMode()) {
      const currentToken = localStorage.getItem(STORAGE_KEYS.TOKEN);
      if (currentToken && currentToken.startsWith('mock_')) {
        const newToken = currentToken + '_refreshed';
        localStorage.setItem(STORAGE_KEYS.TOKEN, newToken);
        return mockResponse({
          success: true,
          message: 'Token refreshed successfully',
          data: newToken
        });
      } else {
        throw new Error('No valid token to refresh');
      }
    } else {
      const response = await apiRequest.post('/auth/refresh');
      localStorage.setItem(STORAGE_KEYS.TOKEN, response.data.data);
      return response;
    }
  },

  // Get current user
  getCurrentUser: () => {
    const userStr = localStorage.getItem(STORAGE_KEYS.USER);
    return userStr ? JSON.parse(userStr) : null;
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);
    return !!token;
  },

  // Get user role
  getUserRole: () => {
    const user = authService.getCurrentUser();
    return user?.role || null;
  }
};

export default authService;
