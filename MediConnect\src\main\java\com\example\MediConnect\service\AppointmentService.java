package com.example.MediConnect.service;

import com.example.MediConnect.dto.AppointmentDTO;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.enums.AppointmentStatus;

import java.time.LocalDateTime;
import java.util.List;

public interface AppointmentService {
    
    AppointmentDTO createAppointment(AppointmentRequest appointmentRequest);
    
    AppointmentDTO getAppointmentById(Long appointmentId);
    
    AppointmentDTO updateAppointmentStatus(Long appointmentId, AppointmentStatus status);
    
    List<AppointmentDTO> getAppointmentsByPatient(Long patientId);
    
    List<AppointmentDTO> getAppointmentsByDoctor(Long doctorId);
    
    List<AppointmentDTO> getAppointmentsByClinic(Long clinicId);
    
    List<AppointmentDTO> getAppointmentsByDateRange(LocalDateTime startDate, LocalDateTime endDate);
    
    List<AppointmentDTO> getAppointmentsByStatus(AppointmentStatus status);
    
    void cancelAppointment(Long appointmentId, String reason);
    
    boolean isSlotAvailable(Long doctorId, LocalDateTime appointmentDate, Integer duration);
    
    List<String> getAvailableSlots(Long doctorId, String date);
}
