# MediConnect - Healthcare Management System

MediConnect is a comprehensive healthcare management system built with Spring Boot that provides role-based access for Admins, Doctors, Clinics, and Patients. The system facilitates appointment booking, medical record management, and healthcare administration.

## Features

### Admin Features
- **Approval Management**: Approve/reject clinic and doctor registrations
- **User Management**: View, activate/deactivate users
- **System Reports**: Basic analytics and usage reports

### Doctor Features
- **Profile Management**: Update personal and professional information
- **Patient Management**: View assigned patients and log diagnoses
- **Follow-up Scheduling**: Schedule follow-up appointments for patients

### Clinic Features
- **Staff Management**: Add/remove doctors and clinic staff
- **Appointment Overview**: View upcoming appointments
- **Announcements**: Post announcements

### Patient Features
- **Appointment Booking**: Book appointments based on doctor availability
- **Search Functionality**: Find doctors/clinics by specialty
- **Medical History**: View basic medical history (diagnoses, prescriptions)

## Technology Stack

- **Backend**: Spring Boot 3.4.5
- **Database**: MySQL 8.0
- **Security**: Spring Security with JWT
- **ORM**: Spring Data JPA with Hibernate
- **Build Tool**: Maven
- **Java Version**: 17

## Prerequisites

- Java 17 or higher
- MySQL 8.0 or higher
- Maven 3.6 or higher

## Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd MediConnect
```

### 2. Database Setup
1. Create a MySQL database named `mediconnect`
2. Update database credentials in `src/main/resources/application.properties`

```properties
spring.datasource.url=***********************************************************************
spring.datasource.username=your_username
spring.datasource.password=your_password
```

### 3. Build and Run
```bash
mvn clean install
mvn spring-boot:run
```

The application will start on `http://localhost:8083`

## API Documentation

### Authentication Endpoints
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `POST /api/auth/logout` - User logout

### Admin Endpoints
- `GET /api/admin/clinics/pending` - Get pending clinic approvals
- `PUT /api/admin/clinics/{id}/approve` - Approve clinic
- `GET /api/admin/doctors/pending` - Get pending doctor approvals
- `PUT /api/admin/doctors/{id}/approve` - Approve doctor
- `GET /api/admin/reports/overview` - Get system overview

### Doctor Endpoints
- `GET /api/doctors/profile` - Get doctor profile
- `GET /api/doctors/patients` - Get assigned patients
- `POST /api/doctors/patients/{id}/diagnosis` - Log patient diagnosis
- `POST /api/doctors/followups` - Schedule follow-up

### Clinic Endpoints
- `GET /api/clinics/profile` - Get clinic profile
- `GET /api/clinics/appointments` - Get clinic appointments
- `POST /api/clinics/announcements` - Create announcement

### Patient Endpoints
- `POST /api/patients/appointments` - Book appointment
- `GET /api/patients/appointments` - Get patient appointments
- `GET /api/patients/search/doctors` - Search doctors by specialty
- `GET /api/patients/medical-history` - Get medical history

## Default Users

The system comes with sample data including:

### Admin User
- **Email**: <EMAIL>
- **Password**: admin123
- **Role**: ADMIN

### Clinic User
- **Email**: <EMAIL>
- **Password**: clinic123
- **Role**: CLINIC

### Doctor User
- **Email**: <EMAIL>
- **Password**: doctor123
- **Role**: DOCTOR

### Patient User
- **Email**: <EMAIL>
- **Password**: patient123
- **Role**: PATIENT

## Project Structure

```
src/
├── main/
│   ├── java/com/example/MediConnect/
│   │   ├── controller/          # REST Controllers
│   │   ├── dto/                 # Data Transfer Objects
│   │   ├── entity/              # JPA Entities
│   │   ├── enums/               # Enumerations
│   │   ├── exception/           # Exception Handlers
│   │   ├── repository/          # JPA Repositories
│   │   ├── security/            # Security Configuration
│   │   ├── service/             # Business Logic
│   │   ├── util/                # Utility Classes
│   │   └── config/              # Configuration Classes
│   └── resources/
│       ├── application.properties
│       └── data.sql             # Sample Data
└── test/                        # Test Classes
```

## Security

The application uses JWT (JSON Web Tokens) for authentication and authorization. Each API request (except authentication endpoints) requires a valid JWT token in the Authorization header:

```
Authorization: Bearer <jwt-token>
```

## Database Schema

The application uses the following main entities:
- **User** (Base entity for all user types)
- **Admin**, **Doctor**, **Clinic**, **Patient** (User subtypes)
- **Appointment** (Appointment bookings)
- **Diagnosis** (Medical diagnoses)
- **Prescription** (Medication prescriptions)
- **FollowUp** (Follow-up appointments)
- **Announcement** (Clinic announcements)
- **Speciality** (Medical specialties)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Commit your changes
4. Push to the branch
5. Create a Pull Request

## License

This project is licensed under the MIT License.

## Support

For support and questions, please contact the development team or create an issue in the repository.
