package com.example.MediConnect.dto;

import com.example.MediConnect.enums.DoctorStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorDTO {
    private Long id;
    private String name;
    private String email;
    private String phoneNumber;
    private String address;
    private String dateOfBirth;
    private String gender;
    private String medicalLicense;
    private String specialtyName;
    private Integer yearsOfExperience;
    private String qualification;
    private DoctorStatus doctorStatus;
    private Long clinicId;
    private String clinicName;
    private Double consultationFee;
    private String bio;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
