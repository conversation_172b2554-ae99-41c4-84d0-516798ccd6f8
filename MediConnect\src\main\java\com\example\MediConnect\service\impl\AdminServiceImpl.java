package com.example.MediConnect.service.impl;

import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.example.MediConnect.dto.ClinicDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.response.SystemReportResponse;
import com.example.MediConnect.entity.Clinic;
import com.example.MediConnect.entity.Doctor;
import com.example.MediConnect.entity.User;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.enums.ClinicStatus;
import com.example.MediConnect.enums.DoctorStatus;
import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.UserStatus;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.AppointmentRepository;
import com.example.MediConnect.repository.ClinicRepository;
import com.example.MediConnect.repository.DoctorRepository;
import com.example.MediConnect.repository.PatientRepository;
import com.example.MediConnect.repository.UserRepository;
import com.example.MediConnect.service.AdminService;
import com.example.MediConnect.service.DataInitializationService;
import com.example.MediConnect.util.Constants;

@Service
@Transactional
public class AdminServiceImpl implements AdminService {

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ClinicRepository clinicRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private DataInitializationService dataInitializationService;

    @Override
    public List<ClinicDTO> getPendingClinicApprovals() {
        List<Clinic> pendingClinics = clinicRepository.findByClinicStatus(ClinicStatus.PENDING_APPROVAL);
        return pendingClinics.stream()
                .map(this::convertToClinicDTO)
                .collect(Collectors.toList());
    }

    @Override
    public void approveClinic(Long clinicId) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));

        clinic.setClinicStatus(ClinicStatus.ACTIVE);
        clinic.setStatus(UserStatus.ACTIVE);
        clinicRepository.save(clinic);
    }

    @Override
    public void rejectClinic(Long clinicId) {
        Clinic clinic = clinicRepository.findById(clinicId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.CLINIC_NOT_FOUND));

        clinic.setClinicStatus(ClinicStatus.REJECTED);
        clinic.setStatus(UserStatus.REJECTED);
        clinicRepository.save(clinic);
    }

    @Override
    public List<DoctorDTO> getPendingDoctorApprovals() {
        List<Doctor> pendingDoctors = doctorRepository.findByDoctorStatus(DoctorStatus.PENDING_APPROVAL);
        return pendingDoctors.stream()
                .map(this::convertToDoctorDTO)
                .collect(Collectors.toList());
    }

    @Override
    public void approveDoctor(Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));

        doctor.setDoctorStatus(DoctorStatus.ACTIVE);
        doctor.setStatus(UserStatus.ACTIVE);
        doctorRepository.save(doctor);
    }

    @Override
    public void rejectDoctor(Long doctorId) {
        Doctor doctor = doctorRepository.findById(doctorId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));

        doctor.setDoctorStatus(DoctorStatus.REJECTED);
        doctor.setStatus(UserStatus.REJECTED);
        doctorRepository.save(doctor);
    }

    @Override
    public List<User> getAllUsers() {
        return userRepository.findAll();
    }

    @Override
    public User getUserById(Long userId) {
        return userRepository.findById(userId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.USER_NOT_FOUND));
    }

    @Override
    public void activateUser(Long userId) {
        User user = getUserById(userId);
        user.setStatus(UserStatus.ACTIVE);
        userRepository.save(user);
    }

    @Override
    public void deactivateUser(Long userId) {
        User user = getUserById(userId);
        user.setStatus(UserStatus.INACTIVE);
        userRepository.save(user);
    }

    @Override
    public List<User> getUsersByStatus(UserStatus status) {
        return userRepository.findByStatus(status);
    }

    @Override
    public SystemReportResponse getSystemOverview() {
        SystemReportResponse report = new SystemReportResponse();

        report.setTotalUsers(userRepository.count());
        report.setTotalDoctors(userRepository.countByRole(Role.DOCTOR));
        report.setTotalClinics(userRepository.countByRole(Role.CLINIC));
        report.setTotalPatients(userRepository.countByRole(Role.PATIENT));
        report.setTotalAppointments(appointmentRepository.count());

        report.setPendingDoctorApprovals(doctorRepository.countByStatus(DoctorStatus.PENDING_APPROVAL));
        report.setPendingClinicApprovals(clinicRepository.countByStatus(ClinicStatus.PENDING_APPROVAL));

        report.setCompletedAppointments(appointmentRepository.countByStatus(AppointmentStatus.COMPLETED));
        report.setCancelledAppointments(appointmentRepository.countByStatus(AppointmentStatus.CANCELLED));

        return report;
    }

    @Override
    public SystemReportResponse getUserStatistics() {
        SystemReportResponse report = new SystemReportResponse();

        report.setTotalUsers(userRepository.count());
        report.setTotalDoctors(userRepository.countByRole(Role.DOCTOR));
        report.setTotalClinics(userRepository.countByRole(Role.CLINIC));
        report.setTotalPatients(userRepository.countByRole(Role.PATIENT));

        return report;
    }

    @Override
    public SystemReportResponse getAppointmentStatistics() {
        SystemReportResponse report = new SystemReportResponse();

        report.setTotalAppointments(appointmentRepository.count());
        report.setCompletedAppointments(appointmentRepository.countByStatus(AppointmentStatus.COMPLETED));
        report.setCancelledAppointments(appointmentRepository.countByStatus(AppointmentStatus.CANCELLED));

        return report;
    }

    @Override
    public void initializeSampleData() {
        try {
            // Force sample data initialization by calling the service directly
            dataInitializationService.forceInitializeSampleData();
        } catch (Exception e) {
            throw new RuntimeException("Failed to initialize sample data", e);
        }
    }

    private ClinicDTO convertToClinicDTO(Clinic clinic) {
        ClinicDTO dto = new ClinicDTO();
        dto.setId(clinic.getId());
        dto.setName(clinic.getName());
        dto.setEmail(clinic.getEmail());
        dto.setPhoneNumber(clinic.getPhoneNumber());
        dto.setAddress(clinic.getAddress());
        dto.setClinicName(clinic.getClinicName());
        dto.setLicenseNumber(clinic.getLicenseNumber());
        dto.setDescription(clinic.getDescription());
        dto.setClinicStatus(clinic.getClinicStatus());
        dto.setOperatingHours(clinic.getOperatingHours());
        dto.setEmergencyContact(clinic.getEmergencyContact());
        dto.setWebsiteUrl(clinic.getWebsiteUrl());
        dto.setCreatedAt(clinic.getCreatedAt());
        dto.setUpdatedAt(clinic.getUpdatedAt());

        if (clinic.getDoctors() != null) {
            dto.setTotalDoctors(clinic.getDoctors().size());
        }
        if (clinic.getClinicStaff() != null) {
            dto.setTotalStaff(clinic.getClinicStaff().size());
        }

        return dto;
    }

    private DoctorDTO convertToDoctorDTO(Doctor doctor) {
        DoctorDTO dto = new DoctorDTO();
        dto.setId(doctor.getId());
        dto.setName(doctor.getName());
        dto.setEmail(doctor.getEmail());
        dto.setPhoneNumber(doctor.getPhoneNumber());
        dto.setAddress(doctor.getAddress());
        dto.setDateOfBirth(doctor.getDateOfBirth());
        dto.setGender(doctor.getGender());
        dto.setMedicalLicense(doctor.getMedicalLicense());
        dto.setYearsOfExperience(doctor.getYearsOfExperience());
        dto.setQualification(doctor.getQualification());
        dto.setDoctorStatus(doctor.getDoctorStatus());
        dto.setConsultationFee(doctor.getConsultationFee());
        dto.setBio(doctor.getBio());
        dto.setCreatedAt(doctor.getCreatedAt());
        dto.setUpdatedAt(doctor.getUpdatedAt());

        if (doctor.getSpeciality() != null) {
            dto.setSpecialtyName(doctor.getSpeciality().getName().toString());
        }

        if (doctor.getClinic() != null) {
            dto.setClinicId(doctor.getClinic().getId());
            dto.setClinicName(doctor.getClinic().getClinicName());
        }

        return dto;
    }
}
