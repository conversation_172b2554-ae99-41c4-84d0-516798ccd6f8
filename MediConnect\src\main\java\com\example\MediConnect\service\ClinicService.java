package com.example.MediConnect.service;

import com.example.MediConnect.dto.ClinicDTO;
import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.AppointmentDTO;
import com.example.MediConnect.dto.AnnouncementDTO;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.dto.request.AnnouncementRequest;
import com.example.MediConnect.entity.User;

import java.util.List;

public interface ClinicService {
    
    // Profile Management
    ClinicDTO getClinicProfile(Long clinicId);
    ClinicDTO updateClinicProfile(Long clinicId, ClinicDTO clinicDTO);
    
    // Staff Management
    List<User> getClinicStaff(Long clinicId);
    DoctorDTO addDoctorToClinic(Long clinicId, DoctorRequest doctorRequest);
    void removeDoctorFromClinic(Long clinicId, Long doctorId);
    User addClinicStaff(Long clinicId, User staffMember);
    void removeClinicStaff(Long clinicId, Long staffId);
    
    // Appointment Management
    List<AppointmentDTO> getClinicAppointments(Long clinicId);
    List<AppointmentDTO> getUpcomingAppointments(Long clinicId);
    List<AppointmentDTO> getTodayAppointments(Long clinicId);
    
    // Announcements
    List<AnnouncementDTO> getClinicAnnouncements(Long clinicId);
    AnnouncementDTO createAnnouncement(Long clinicId, AnnouncementRequest announcementRequest);
    AnnouncementDTO updateAnnouncement(Long announcementId, AnnouncementRequest announcementRequest);
    void deleteAnnouncement(Long announcementId);
}
