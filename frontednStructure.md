Assumptions for Design:
Clean & Modern: We'll aim for a clean, modern, and trustworthy look, suitable for a healthcare application.
Color Palette: Primary: Blue (trust, calmness), Secondary: Teal/Green (health, growth), Accents: Subtle grays, success green, error red, warning yellow.
Typography: Clear, legible sans-serif fonts.
Layout: Standard dashboard layout with a sidebar for navigation, a header, and a main content area.
Step 1: Project Setup (Vite + React + Tailwind CSS)
Create Vite Project:
npm create vite@latest mediconnect-frontend -- --template react
cd mediconnect-frontend
npm install
Use code with caution.
Bash
Install Tailwind CSS & Dependencies:
npm install -D tailwindcss postcss autoprefixer
npx tailwindcss init -p
Use code with caution.
Bash
Configure Tailwind:
Open tailwind.config.js and update the content array:
/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          light: '#67e8f9', // cyan-300
          DEFAULT: '#06b6d4', // cyan-500
          dark: '#0e7490', // cyan-700
        },
        secondary: {
          light: '#5eead4', // teal-300
          DEFAULT: '#14b8a6', // teal-500
          dark: '#0f766e', // teal-700
        },
        accent: {
          DEFAULT: '#f59e0b', // amber-500
        },
        neutral: {
          lightest: '#f8fafc', // slate-50
          light: '#f1f5f9',    // slate-100
          DEFAULT: '#64748b',  // slate-500
          dark: '#334155',    // slate-700
          darkest: '#0f172a',  // slate-900
        },
        success: '#22c55e', // green-500
        danger: '#ef4444',  // red-500
        warning: '#eab308', // yellow-500
      }
    },
  },
  plugins: [
    require('@tailwindcss/forms'), // For better default form styling
  ],
}
Use code with caution.
JavaScript
Add Tailwind Directives:
Create src/styles/tailwind.css (or use src/index.css if preferred, but structure suggests styles/)
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Optional: Add global styles or custom base styles here */
body {
  @apply bg-neutral-lightest text-neutral-dark;
  font-family: 'Inter', sans-serif; /* Add Inter or a similar font via CDN in index.html */
}
Use code with caution.
Css
If you created src/styles/tailwind.css, import it in src/main.jsx (or index.js if you renamed it):
// src/main.jsx
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import './styles/tailwind.css' // Or './index.css' if you put directives there

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
)
Use code with caution.
JavaScript
Install React Router DOM & other common libraries:
npm install react-router-dom lucide-react  # lucide-react for icons
npm install date-fns # for date utilities
Use code with caution.
Bash
Add a nice font to public/index.html:
<!doctype html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/favicon.ico" /> <!-- Update favicon -->
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>MediConnect</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">
  </head>
  <body>
    <div id="root"></div>
    <script type="module" src="/src/main.jsx"></script>
  </body>
</html>
Use code with caution.
Html
Step 2: Create Folder Structure and Basic Files
Create all the folders and empty .jsx files as per your provided structure.
For example:
touch src/index.js src/reportWebVitals.js
mkdir -p src/components/common src/components/auth ...etc.
And then touch src/components/common/Header.jsx ...etc.
Step 3: Implement Core Layout Components
src/components/common/Header.jsx:
import React from 'react';
import { Link } from 'react-router-dom';
import { Bell, UserCircle, Menu } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth'; // We'll create this hook

const Header = ({ onToggleSidebar }) => {
  const { user, logout } = useAuth() || { user: null, logout: () => console.log("Logout (mock)") }; // Default if context not ready

  return (
    <header className="bg-white shadow-md sticky top-0 z-40">
      <div className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center">
          <button
            onClick={onToggleSidebar}
            className="text-neutral-DEFAULT hover:text-primary md:hidden mr-4"
            aria-label="Toggle sidebar"
          >
            <Menu size={24} />
          </button>
          <Link to="/" className="text-2xl font-bold text-primary">
            Medi<span className="text-secondary-dark">Connect</span>
          </Link>
        </div>
        <div className="flex items-center space-x-4">
          <button className="relative text-neutral-DEFAULT hover:text-primary">
            <Bell size={24} />
            <span className="absolute top-0 right-0 block h-2 w-2 rounded-full bg-danger ring-2 ring-white" />
          </button>
          {user ? (
            <div className="relative group">
              <button className="flex items-center text-neutral-DEFAULT hover:text-primary">
                <UserCircle size={28} className="mr-2" />
                <span className="hidden sm:block">{user.name || 'User Profile'}</span>
              </button>
              <div className="absolute right-0 mt-2 w-48 bg-white rounded-md shadow-lg py-1 z-50 hidden group-hover:block">
                <Link to="/profile" className="block px-4 py-2 text-sm text-neutral-dark hover:bg-neutral-light">Profile</Link>
                <button
                  onClick={logout}
                  className="block w-full text-left px-4 py-2 text-sm text-neutral-dark hover:bg-neutral-light"
                >
                  Logout
                </button>
              </div>
            </div>
          ) : (
            <Link to="/login" className="text-sm font-medium text-primary hover:text-primary-dark">
              Login
            </Link>
          )}
        </div>
      </div>
    </header>
  );
};

export default Header;
Use code with caution.
Jsx
src/components/common/Sidebar.jsx:
import React from 'react';
import { NavLink, Link } from 'react-router-dom';
import { LayoutDashboard, Users, Stethoscope, Building, UserCog, LogOut, ActivitySquare } from 'lucide-react'; // Example icons
import { useAuth } from '../../hooks/useAuth';

const commonLinks = [
  // { to: '/dashboard', text: 'Dashboard', icon: LayoutDashboard },
];

const roleLinks = {
  ADMIN: [
    { to: '/admin/dashboard', text: 'Admin Dashboard', icon: UserCog },
    { to: '/admin/user-management', text: 'User Management', icon: Users },
    { to: '/admin/doctor-approval', text: 'Doctor Approvals', icon: Stethoscope },
    { to: '/admin/clinic-management', text: 'Clinic Management', icon: Building },
    { to: '/admin/reports', text: 'System Reports', icon: ActivitySquare },
  ],
  DOCTOR: [
    { to: '/doctor/dashboard', text: 'Doctor Dashboard', icon: LayoutDashboard },
    { to: '/doctor/appointments', text: 'Appointments', icon: Stethoscope },
    { to: '/doctor/patients', text: 'Patients', icon: Users },
  ],
  PATIENT: [
    { to: '/patient/dashboard', text: 'Patient Dashboard', icon: LayoutDashboard },
    { to: '/patient/appointments', text: 'My Appointments', icon: Stethoscope },
    { to: '/patient/book-appointment', text: 'Book Appointment', icon: Users },
  ],
  CLINIC: [
    { to: '/clinic/dashboard', text: 'Clinic Dashboard', icon: LayoutDashboard },
    { to: '/clinic/staff', text: 'Staff Management', icon: Users },
    { to: '/clinic/appointments', text: 'Appointments', icon: Stethoscope },
  ],
};

const Sidebar = ({ isOpen, onToggleSidebar }) => {
  const { user, logout } = useAuth() || { user: null, logout: () => {} };
  const activeRole = user?.role || null; // e.g., 'ADMIN', 'DOCTOR'

  const links = activeRole ? [...commonLinks, ...roleLinks[activeRole]] : commonLinks;

  const baseLinkClasses = "flex items-center px-4 py-3 text-neutral-darkest hover:bg-primary-light hover:text-primary-dark rounded-lg transition-colors duration-150";
  const activeLinkClasses = "bg-primary-light text-primary-dark font-semibold";

  if (!user) return null; // Don't show sidebar if not logged in, or show a minimal version

  return (
    <>
      {/* Overlay for mobile */}
      {isOpen && (
        <div
          className="fixed inset-0 z-30 bg-black opacity-50 md:hidden"
          onClick={onToggleSidebar}
        ></div>
      )}
      <aside
        className={`fixed inset-y-0 left-0 z-40 w-64 bg-white shadow-lg transform ${
          isOpen ? 'translate-x-0' : '-translate-x-full'
        } md:translate-x-0 md:sticky md:top-16 md:h-[calc(100vh-4rem)] transition-transform duration-300 ease-in-out overflow-y-auto p-4`}
      >
        <nav className="mt-4">
          <ul>
            {links.map((link) => (
              <li key={link.to} className="mb-2">
                <NavLink
                  to={link.to}
                  className={({ isActive }) =>
                    `${baseLinkClasses} ${isActive ? activeLinkClasses : ''}`
                  }
                  onClick={onToggleSidebar} // Close sidebar on mobile after click
                >
                  {link.icon && <link.icon size={20} className="mr-3" />}
                  {link.text}
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
        <div className="mt-auto p-4 absolute bottom-0 w-full left-0">
          <button
            onClick={() => {
              logout();
              if (onToggleSidebar) onToggleSidebar();
            }}
            className={`${baseLinkClasses} w-full text-danger hover:bg-red-100 hover:text-danger`}
          >
            <LogOut size={20} className="mr-3" />
            Logout
          </button>
        </div>
      </aside>
    </>
  );
};

export default Sidebar;
Use code with caution.
Jsx
src/components/layout/Layout.jsx:
import React, { useState } from 'react';
import { Outlet, useLocation } from 'react-router-dom';
import Header from '../common/Header';
import Sidebar from '../common/Sidebar';
import Footer from '../common/Footer';
import { useAuth } from '../../hooks/useAuth'; // We'll create this

const Layout = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const { user } = useAuth() || { user: null }; // Default if context not ready
  const location = useLocation();

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Determine if sidebar should be shown based on route or auth status
  // For simplicity, we show sidebar if user is logged in and not on auth pages
  const showSidebar = user && !['/login', '/register'].includes(location.pathname);

  return (
    <div className="flex flex-col min-h-screen">
      <Header onToggleSidebar={toggleSidebar} />
      <div className="flex flex-1">
        {showSidebar && <Sidebar isOpen={isSidebarOpen} onToggleSidebar={toggleSidebar} />}
        <main className={`flex-1 p-4 sm:p-6 lg:p-8 overflow-y-auto bg-neutral-lightest ${showSidebar ? 'md:ml-64' : ''}`}>
          <Outlet /> {/* Page content will be rendered here */}
        </main>
      </div>
      {!user && <Footer />} {/* Optionally show footer only on public pages */}
    </div>
  );
};

export default Layout;
Use code with caution.
Jsx
src/components/common/Footer.jsx:
import React from 'react';

const Footer = () => {
  return (
    <footer className="bg-neutral-dark text-neutral-light p-8 text-center">
      <div className="container mx-auto">
        <p>© {new Date().getFullYear()} MediConnect. All rights reserved.</p>
        <div className="mt-2">
          <a href="/privacy-policy" className="hover:text-primary-light px-2">Privacy Policy</a>
          <a href="/terms-of-service" className="hover:text-primary-light px-2">Terms of Service</a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
Use code with caution.
Jsx
Step 4: Authentication Context and Hook
src/context/AuthContext.jsx:
import React, { createContext, useState, useEffect } from 'react';
// Mock auth service, replace with actual API calls later
import { login as mockLogin, register as mockRegister } from '../services/authService';

export const AuthContext = createContext(null);

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    // Try to load user from localStorage (e.g., on page refresh)
    try {
      const storedUser = localStorage.getItem('mediconnect_user');
      const token = localStorage.getItem('mediconnect_token');
      if (storedUser && token) {
        setUser(JSON.parse(storedUser));
      }
    } catch (e) {
      console.error("Failed to parse user from localStorage", e);
      localStorage.removeItem('mediconnect_user');
      localStorage.removeItem('mediconnect_token');
    }
    setLoading(false);
  }, []);

  const login = async (credentials) => {
    setError(null);
    setLoading(true);
    try {
      // const response = await authService.login(credentials); // REAL API
      const response = await mockLogin(credentials); // MOCK
      setUser(response.user);
      localStorage.setItem('mediconnect_user', JSON.stringify(response.user));
      localStorage.setItem('mediconnect_token', response.token);
      setLoading(false);
      return response;
    } catch (err) {
      setError(err.message || 'Login failed');
      setLoading(false);
      throw err;
    }
  };

  const register = async (userData) => {
    setError(null);
    setLoading(true);
    try {
      // const response = await authService.register(userData); // REAL API
      const response = await mockRegister(userData); // MOCK
      setUser(response.user);
      localStorage.setItem('mediconnect_user', JSON.stringify(response.user));
      localStorage.setItem('mediconnect_token', response.token);
      setLoading(false);
      return response;
    } catch (err) {
      setError(err.message || 'Registration failed');
      setLoading(false);
      throw err;
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('mediconnect_user');
    localStorage.removeItem('mediconnect_token');
    // authService.logout(); // Call actual logout endpoint if needed
    console.log("User logged out");
  };

  return (
    <AuthContext.Provider value={{ user, loading, error, login, register, logout, isAuthenticated: !!user }}>
      {children}
    </AuthContext.Provider>
  );
};
Use code with caution.
Jsx
src/hooks/useAuth.js:
import { useContext } from 'react';
import { AuthContext } from '../context/AuthContext';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
Use code with caution.
Jsx
Step 5: Mock Services
src/services/authService.js (Mock Implementation):
// Mock users - in a real app, this comes from the backend
const mockUsers = [
  { id: 1, name: 'Admin User', email: '<EMAIL>', password: 'password', role: 'ADMIN' },
  { id: 2, name: 'Dr. Emily Carter', email: '<EMAIL>', password: 'password', role: 'DOCTOR', specialty: 'Cardiology' },
  { id: 3, name: 'John Patient', email: '<EMAIL>', password: 'password', role: 'PATIENT' },
  { id: 4, name: 'Clinic Admin', email: '<EMAIL>', password: 'password', role: 'CLINIC' },
];

let nextUserId = 5;

export const login = async (credentials) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      const user = mockUsers.find(u => u.email === credentials.email && u.password === credentials.password);
      if (user) {
        const { password, ...userWithoutPassword } = user;
        resolve({
          token: `mock-jwt-token-for-${user.id}`,
          user: userWithoutPassword,
          email: user.email,
          role: user.role,
          userId: user.id,
          name: user.name,
          message: "Authentication successful"
        });
      } else {
        reject({ message: 'Invalid email or password' });
      }
    }, 500);
  });
};

export const register = async (userData) => {
  return new Promise((resolve, reject) => {
    setTimeout(() => {
      if (mockUsers.some(u => u.email === userData.email)) {
        reject({ message: 'Email already exists' });
        return;
      }
      const newUser = {
        id: nextUserId++,
        name: userData.name,
        email: userData.email,
        password: userData.password, // In real app, password would be hashed by backend
        role: userData.role || 'PATIENT', // Default to PATIENT if not specified
        // Add other fields based on role from DTOs
        ...(userData.role === 'DOCTOR' && {
          medicalLicense: userData.medicalLicense,
          specialtyName: userData.specialtyName || "General Practice",
          yearsOfExperience: userData.yearsOfExperience,
          qualification: userData.qualification,
          doctorStatus: "PENDING_APPROVAL", // Default status for new doctors
          consultationFee: userData.consultationFee,
          bio: userData.bio,
        }),
        ...(userData.role === 'PATIENT' && {
            patientId: `PAT${Date.now()}`,
            emergencyContactName: userData.emergencyContactName,
            bloodGroup: userData.bloodGroup,
        }),
        // Add other DTO fields
      };
      mockUsers.push(newUser);
      const { password, ...userWithoutPassword } = newUser;
      resolve({
        token: `mock-jwt-token-for-${newUser.id}`,
        user: userWithoutPassword,
        email: newUser.email,
        role: newUser.role,
        userId: newUser.id,
        name: newUser.name,
        message: "Registration successful"
      });
    }, 500);
  });
};

export const logout = async () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      console.log("Mock logout successful");
      resolve({ success: true, message: "Logout successful" });
    }, 200);
  });
};

export const validateToken = async (token) => {
    return new Promise((resolve, reject) => {
        setTimeout(() => {
            if (token && token.startsWith('mock-jwt-token')) {
                resolve({ success: true, message: "Token is valid" });
            } else {
                reject({ success: false, message: "Token is invalid" });
            }
        }, 300);
    });
};
Use code with caution.
JavaScript
(Continue creating mock services for adminService.js, doctorService.js etc., returning hardcoded data based on the API responses you provided.)
Step 6: Basic Reusable Components (src/components/common)
src/components/common/Button.jsx:
import React from 'react';

const Button = ({
  children,
  onClick,
  type = 'button',
  variant = 'primary', // primary, secondary, danger, outline, ghost
  size = 'md', // sm, md, lg
  disabled = false,
  isLoading = false,
  className = '',
  ...props
}) => {
  const baseStyles = 'font-semibold rounded-lg focus:outline-none focus:ring-2 focus:ring-opacity-50 transition-all duration-150 ease-in-out flex items-center justify-center';

  const variantStyles = {
    primary: 'bg-primary text-white hover:bg-primary-dark focus:ring-primary',
    secondary: 'bg-secondary text-white hover:bg-secondary-dark focus:ring-secondary',
    danger: 'bg-danger text-white hover:bg-red-600 focus:ring-danger',
    outline: 'border border-primary text-primary hover:bg-primary-light focus:ring-primary',
    ghost: 'text-primary hover:bg-primary-light focus:ring-primary',
    link: 'text-primary hover:underline p-0',
  };

  const sizeStyles = {
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  const disabledStyles = 'opacity-50 cursor-not-allowed';

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || isLoading}
      className={`${baseStyles} ${variantStyles[variant]} ${sizeStyles[size]} ${ (disabled || isLoading) ? disabledStyles : ''} ${className}`}
      {...props}
    >
      {isLoading ? (
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      ) : children}
    </button>
  );
};

export default Button;
Use code with caution.
Jsx
src/components/common/Input.jsx:
import React from 'react';

const Input = ({
  id,
  label,
  type = 'text',
  value,
  onChange,
  placeholder,
  error,
  required = false,
  className = '',
  labelClassName = '',
  inputClassName = '',
  ...props
}) => {
  return (
    <div className={`mb-4 ${className}`}>
      {label && (
        <label htmlFor={id} className={`block text-sm font-medium text-neutral-dark mb-1 ${labelClassName}`}>
          {label} {required && <span className="text-danger">*</span>}
        </label>
      )}
      <input
        type={type}
        id={id}
        name={id}
        value={value}
        onChange={onChange}
        placeholder={placeholder}
        required={required}
        className={`w-full px-3 py-2 border ${error ? 'border-danger focus:ring-danger' : 'border-neutral-light focus:border-primary'} rounded-lg shadow-sm focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm ${inputClassName}`}
        {...props}
      />
      {error && <p className="mt-1 text-xs text-danger">{error}</p>}
    </div>
  );
};

export default Input;
Use code with caution.
Jsx
src/components/common/Card.jsx:
import React from 'react';

const Card = ({ title, children, className = '', titleClassName='', actions }) => {
  return (
    <div className={`bg-white shadow-lg rounded-xl overflow-hidden ${className}`}>
      {title && (
        <div className={`p-4 sm:p-6 border-b border-neutral-light flex justify-between items-center ${titleClassName}`}>
          <h3 className="text-lg sm:text-xl font-semibold text-neutral-darkest">{title}</h3>
          {actions && <div className="flex space-x-2">{actions}</div>}
        </div>
      )}
      <div className="p-4 sm:p-6">
        {children}
      </div>
    </div>
  );
};

export default Card;
Use code with caution.
Jsx
Step 7: Routing Setup (src/App.jsx)
// src/App.jsx
import React from 'react';
import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { AuthProvider } from './context/AuthContext';
import Layout from './components/layout/Layout';
import ProtectedRoute from './components/layout/ProtectedRoute';
import RoleBasedRoute from './components/layout/RoleBasedRoute';

// Pages
import Home from './pages/Home';
import About from './pages/About';
import Contact from './pages/Contact';
import NotFound from './pages/NotFound';
import Login from './components/auth/Login'; // Assuming Login is a component for now
import Register from './components/auth/Register'; // Assuming Register is a component

// Dashboard Components (will be actual pages later or wrapped)
import AdminDashboard from './components/admin/AdminDashboard';
import DoctorDashboard from './components/doctor/DoctorDashboard';
import PatientDashboard from './components/patient/PatientDashboard';
import ClinicDashboard from './components/clinic/ClinicDashboard';
import UserManagement from './components/admin/UserManagement';
import DoctorApproval from './components/admin/DoctorApproval';

// Basic Page Components
const createPage = (title, content = 'Content coming soon...') => () => (
  <div><h1 className="text-2xl font-semibold mb-4">{title}</h1><p>{content}</p></div>
);

// For components that are not yet created, use placeholders
const PlaceholderComponent = ({ name }) => <div className="p-4 bg-yellow-100 border border-yellow-300 rounded-md">Placeholder for <strong>{name}</strong></div>;


function App() {
  return (
    <AuthProvider>
      <Router>
        <Routes>
          <Route path="/login" element={<Login />} />
          <Route path="/register" element={<Register />} />
          
          <Route element={<Layout />}>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/contact" element={<Contact />} />
            {/* Add other public pages here */}

            {/* Protected Routes - Example for Admin */}
            <Route element={<ProtectedRoute />}>
              <Route element={<RoleBasedRoute allowedRoles={['ADMIN']} />}>
                <Route path="/admin/dashboard" element={<AdminDashboard />} />
                <Route path="/admin/user-management" element={<UserManagement />} />
                <Route path="/admin/doctor-approval" element={<DoctorApproval />} />
                {/* Add other admin routes */}
              </Route>

              <Route element={<RoleBasedRoute allowedRoles={['DOCTOR']} />}>
                <Route path="/doctor/dashboard" element={<DoctorDashboard />} />
                {/* Add other doctor routes */}
              </Route>
              
              <Route element={<RoleBasedRoute allowedRoles={['PATIENT']} />}>
                <Route path="/patient/dashboard" element={<PatientDashboard />} />
                {/* Add other patient routes */}
              </Route>

              <Route element={<RoleBasedRoute allowedRoles={['CLINIC']} />}>
                <Route path="/clinic/dashboard" element={<ClinicDashboard />} />
                {/* Add other clinic routes */}
              </Route>

              {/* Generic profile page accessible by any logged-in user */}
              <Route path="/profile" element={<PlaceholderComponent name="User Profile Page" />} />

            </Route>
            
            <Route path="*" element={<NotFound />} />
          </Route>
        </Routes>
      </Router>
    </AuthProvider>
  );
}

export default App;
Use code with caution.
Jsx
Step 8: Protected and Role-Based Route Components
src/components/layout/ProtectedRoute.jsx:
import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import Loading from '../common/Loading'; // Create this component

const ProtectedRoute = () => {
  const { isAuthenticated, loading } = useAuth();
  const location = useLocation();

  if (loading) {
    return <div className="flex justify-center items-center h-screen"><Loading /></div>;
  }

  if (!isAuthenticated) {
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  return <Outlet />;
};

export default ProtectedRoute;
Use code with caution.
Jsx
src/components/layout/RoleBasedRoute.jsx:
import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import Loading from '../common/Loading';

const RoleBasedRoute = ({ allowedRoles }) => {
  const { user, loading, isAuthenticated } = useAuth();
  const location = useLocation();

  if (loading) {
    return <div className="flex justify-center items-center h-screen"><Loading /></div>;
  }

  if (!isAuthenticated) {
     // Should be caught by ProtectedRoute, but as a fallback
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  if (user && allowedRoles && !allowedRoles.includes(user.role)) {
    // User is authenticated but doesn't have the required role
    // Redirect to an unauthorized page or their default dashboard
    // For simplicity, redirecting to home or a specific unauthorized page
    console.warn(`User role ${user.role} not in allowed roles: ${allowedRoles.join(', ')} for path ${location.pathname}`);
    return <Navigate to="/unauthorized" state={{ from: location }} replace />;
  }

  return <Outlet />;
};

export default RoleBasedRoute;
Use code with caution.
Jsx
src/components/common/Loading.jsx:
import React from 'react';

const Loading = ({ message = "Loading..." }) => {
  return (
    <div className="flex flex-col items-center justify-center p-10">
      <svg className="animate-spin h-10 w-10 text-primary mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <p className="text-neutral-DEFAULT text-lg">{message}</p>
    </div>
  );
};

export default Loading;
Use code with caution.
Jsx
Step 9: Basic Pages and Auth Components
src/pages/Home.jsx:
import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import Card from '../components/common/Card';
import { useAuth } from '../hooks/useAuth';
import { ShieldCheck, Users, CalendarDays } from 'lucide-react';


const FeatureCard = ({ icon, title, description }) => (
  <Card className="text-center hover:shadow-xl transition-shadow">
    <div className="flex justify-center mb-4 text-primary">{icon}</div>
    <h3 className="text-xl font-semibold mb-2 text-neutral-darkest">{title}</h3>
    <p className="text-neutral-DEFAULT">{description}</p>
  </Card>
);


const Home = () => {
  const { user, isAuthenticated } = useAuth();

  return (
    <div className="space-y-12">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary-light via-primary to-primary-dark text-white py-20 px-6 rounded-lg shadow-xl">
        <div className="container mx-auto text-center">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">Welcome to MediConnect</h1>
          <p className="text-lg md:text-xl mb-8 max-w-2xl mx-auto">
            Your trusted platform for seamless healthcare management. Connect with doctors, manage appointments, and access your medical records securely.
          </p>
          {isAuthenticated ? (
            <Link to={`/${user.role.toLowerCase()}/dashboard`}>
              <Button variant="secondary" size="lg">Go to Dashboard</Button>
            </Link>
          ) : (
            <div className="space-x-4">
              <Link to="/login">
                <Button variant="secondary" size="lg">Login</Button>
              </Link>
              <Link to="/register">
                <Button variant="outline" className="border-white text-white hover:bg-white hover:text-primary" size="lg">Register</Button>
              </Link>
            </div>
          )}
        </div>
      </section>

      {/* Features Section */}
      <section>
        <h2 className="text-3xl font-semibold text-center mb-10 text-neutral-darkest">Our Core Features</h2>
        <div className="grid md:grid-cols-3 gap-8">
          <FeatureCard 
            icon={<CalendarDays size={48} />}
            title="Easy Appointments"
            description="Book and manage your appointments with doctors and clinics effortlessly."
          />
          <FeatureCard
            icon={<ShieldCheck size={48} />}
            title="Secure Medical Records"
            description="Access your health history, diagnoses, and prescriptions anytime, anywhere."
          />
          <FeatureCard
            icon={<Users size={48} />}
            title="Connect with Professionals"
            description="Find qualified doctors and clinics tailored to your healthcare needs."
          />
        </div>
      </section>

      {/* Call to Action or Info Section */}
      {isAuthenticated && user && (
        <section className="bg-white p-8 rounded-lg shadow-md">
            <h2 className="text-2xl font-semibold text-neutral-darkest mb-4">Hello, {user.name}!</h2>
            <p className="text-neutral-DEFAULT mb-6">
              You are logged in as a {user.role.toLowerCase()}. Explore your dashboard to manage your healthcare activities.
            </p>
            <Link to={`/${user.role.toLowerCase()}/dashboard`}>
              <Button>Access Your Dashboard</Button>
            </Link>
        </section>
      )}
    </div>
  );
};

export default Home;
Use code with caution.
Jsx
src/components/auth/Login.jsx:
import React, { useState } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { useAuth } from '../../hooks/useAuth';
import Input from '../common/Input';
import Button from '../common/Button';
import Card from '../common/Card';
import { Mail, Lock } from 'lucide-react';

const Login = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const { login, loading, error } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const from = location.state?.from?.pathname || "/";

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const response = await login({ email, password });
      // Navigate to intended page or role-specific dashboard
      if (response && response.role) {
        const roleDashboardPath = `/${response.role.toLowerCase()}/dashboard`;
        navigate(from === "/" ? roleDashboardPath : from, { replace: true });
      } else {
        navigate(from, { replace: true });
      }
    } catch (err) {
      // Error is handled by useAuth and displayed below
      console.error("Login failed:", err);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-neutral-lightest p-4">
      <Card title="Login to MediConnect" className="w-full max-w-md">
        <form onSubmit={handleSubmit} className="space-y-6">
          <Input
            id="email"
            label="Email Address"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="<EMAIL>"
            required
            icon={<Mail size={18} className="text-neutral-DEFAULT" />}
          />
          <Input
            id="password"
            label="Password"
            type="password"
            value={password}
            onChange={(e) => setPassword(e.target.value)}
            placeholder="••••••••"
            required
            icon={<Lock size={18} className="text-neutral-DEFAULT" />}
          />
          {error && <p className="text-sm text-danger text-center">{error}</p>}
          <Button type="submit" variant="primary" size="lg" className="w-full" isLoading={loading} disabled={loading}>
            {loading ? 'Logging in...' : 'Login'}
          </Button>
        </form>
        <p className="mt-6 text-center text-sm text-neutral-DEFAULT">
          Don't have an account?{' '}
          <Link to="/register" className="font-medium text-primary hover:text-primary-dark">
            Sign up
          </Link>
        </p>
        <div className="mt-4 text-center text-xs text-neutral-DEFAULT">
          <p>Admin: <EMAIL> / password</p>
          <p>Doctor: <EMAIL> / password</p>
          <p>Patient: <EMAIL> / password</p>
          <p>Clinic: <EMAIL> / password</p>
        </div>
      </Card>
    </div>
  );
};

export default Login;
Use code with caution.
Jsx
(Create Register.jsx similarly, with more fields based on your DTOs. I'll skip the full code for brevity here but it would use <Input> and <Button> components and call authContext.register.)
src/pages/NotFound.jsx:
import React from 'react';
import { Link } from 'react-router-dom';
import Button from '../components/common/Button';
import { AlertTriangle } from 'lucide-react';

const NotFound = () => {
  return (
    <div className="flex flex-col items-center justify-center text-center min-h-[calc(100vh-15rem)]">
      <AlertTriangle size={64} className="text-danger mb-6" />
      <h1 className="text-6xl font-bold text-neutral-darkest mb-4">404</h1>
      <h2 className="text-2xl font-semibold text-neutral-dark mb-6">Page Not Found</h2>
      <p className="text-neutral-DEFAULT mb-8 max-w-md">
        Oops! The page you are looking for does not exist. It might have been moved or deleted.
      </p>
      <Link to="/">
        <Button variant="primary" size="lg">
          Go to Homepage
        </Button>
      </Link>
    </div>
  );
};
export default NotFound;
Use code with caution.
Jsx
src/pages/About.jsx, src/pages/Contact.jsx (Simple Placeholders):
// src/pages/About.jsx
import React from 'react';
import Card from '../components/common/Card';

const About = () => (
  <div className="space-y-6">
    <Card title="About MediConnect">
      <p className="text-lg text-neutral-DEFAULT mb-4">
        MediConnect is a revolutionary platform designed to bridge the gap between patients, doctors, and clinics,
        providing a seamless and efficient healthcare experience for everyone.
      </p>
      <p className="text-neutral-DEFAULT mb-4">
        Our mission is to leverage technology to make healthcare more accessible, manageable, and transparent.
        We aim to empower patients with control over their medical information, assist doctors in providing
        optimal care, and help clinics streamline their operations.
      </p>
      <h3 className="text-xl font-semibold text-neutral-darkest mt-6 mb-2">Our Vision</h3>
      <p className="text-neutral-DEFAULT">
        To be the leading digital healthcare ecosystem that enhances the quality of life through connected care.
      </p>
    </Card>
  </div>
);
export default About;

// src/pages/Contact.jsx
import React from 'react';
import Card from '../components/common/Card';
import Input from '../components/common/Input';
import Button from '../components/common/Button';
import { Mail, Phone, MapPin } from 'lucide-react';


const Contact = () => (
  <div className="space-y-8">
    <Card title="Get in Touch">
      <p className="text-neutral-DEFAULT mb-6">
        We'd love to hear from you! Whether you have a question about features, trials, pricing, or anything else, our team is ready to answer all your questions.
      </p>
      <div className="grid md:grid-cols-2 gap-8">
        <div>
          <h3 className="text-xl font-semibold text-neutral-darkest mb-4">Contact Information</h3>
          <div className="space-y-3">
            <p className="flex items-center text-neutral-DEFAULT"><Mail size={20} className="mr-3 text-primary" /> <EMAIL></p>
            <p className="flex items-center text-neutral-DEFAULT"><Phone size={20} className="mr-3 text-primary" /> +****************</p>
            <p className="flex items-center text-neutral-DEFAULT"><MapPin size={20} className="mr-3 text-primary" /> 123 Health St, Wellness City, HC 90210</p>
          </div>
        </div>
        <form onSubmit={(e) => {e.preventDefault(); alert('Message sent (mock)!')}}>
          <Input id="name" label="Full Name" placeholder="John Doe" required />
          <Input id="email" label="Email Address" type="email" placeholder="<EMAIL>" required />
          <Input id="message" label="Message" type="textarea" rows="4" placeholder="Your message..." required />
          <Button type="submit" className="w-full">Send Message</Button>
        </form>
      </div>
    </Card>
  </div>
);
export default Contact;
Use code with caution.
Jsx
Step 10: Dashboard Placeholders (Example: AdminDashboard)
src/components/admin/AdminDashboard.jsx:
import React, { useEffect, useState } from 'react';
import Card from '../common/Card';
import { Users, UserCheck, Activity, BarChart3, AlertCircle } from 'lucide-react'; // Example icons
import Loading from '../common/Loading';
// Mock service, replace with actual API call
// import { getAdminDashboardOverview } from '../../services/adminService';

// MOCK DATA - This would come from adminService.js
const mockAdminOverview = {
  totalUsers: 1500,
  totalDoctors: 250,
  totalClinics: 75,
  totalPatients: 1175,
  totalAppointments: 560,
  pendingDoctorApprovals: 12,
  pendingClinicApprovals: 3,
  todayAppointments: 45,
  completedAppointments: 320,
  cancelledAppointments: 30,
};

const StatCard = ({ title, value, icon, colorClass = 'text-primary' }) => (
  <Card className="shadow-md hover:shadow-lg transition-shadow">
    <div className="flex items-center">
      <div className={`p-3 rounded-full bg-opacity-20 mr-4 ${colorClass.replace('text-', 'bg-')}`}>
        {React.cloneElement(icon, { size: 28, className: colorClass })}
      </div>
      <div>
        <p className="text-sm text-neutral-DEFAULT">{title}</p>
        <p className="text-2xl font-semibold text-neutral-darkest">{value}</p>
      </div>
    </div>
  </Card>
);

const AdminDashboard = () => {
  const [overviewData, setOverviewData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchOverview = async () => {
      setLoading(true);
      try {
        // const data = await getAdminDashboardOverview(); // REAL API CALL
        // Simulating API call
        await new Promise(resolve => setTimeout(resolve, 1000));
        setOverviewData(mockAdminOverview); // Using mock data
        setError(null);
      } catch (err) {
        console.error("Failed to fetch admin overview:", err);
        setError("Could not load dashboard data.");
        setOverviewData(mockAdminOverview); // Fallback to mock on error for UI demo
      } finally {
        setLoading(false);
      }
    };
    fetchOverview();
  }, []);

  if (loading) return <Loading message="Loading Admin Dashboard..." />;
  if (error && !overviewData) return <div className="text-danger text-center p-4">{error}</div>;


  return (
    <div className="space-y-6">
      <h1 className="text-3xl font-semibold text-neutral-darkest">Admin Dashboard</h1>
      
      {error && <div className="p-4 mb-4 text-sm text-red-700 bg-red-100 rounded-lg" role="alert">{error}</div>}

      {overviewData && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          <StatCard title="Total Users" value={overviewData.totalUsers} icon={<Users />} />
          <StatCard title="Total Doctors" value={overviewData.totalDoctors} icon={<UserCheck />} colorClass="text-secondary" />
          <StatCard title="Total Clinics" value={overviewData.totalClinics} icon={<Activity />} colorClass="text-accent" />
          <StatCard title="Total Patients" value={overviewData.totalPatients} icon={<Users />} />
          <StatCard title="Pending Doctor Approvals" value={overviewData.pendingDoctorApprovals} icon={<AlertCircle />} colorClass="text-warning" />
          <StatCard title="Pending Clinic Approvals" value={overviewData.pendingClinicApprovals} icon={<AlertCircle />} colorClass="text-warning" />
          <StatCard title="Appointments Today" value={overviewData.todayAppointments || 0} icon={<BarChart3 />} colorClass="text-green-500" />
          <StatCard title="Total Appointments" value={overviewData.totalAppointments} icon={<BarChart3 />} colorClass="text-indigo-500" />
        </div>
      )}

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card title="Recent Activity">
          <p className="text-neutral-DEFAULT">Activity log will be displayed here...</p>
          {/* Placeholder for a small table or list */}
          <ul className="mt-4 space-y-2">
            <li className="p-2 bg-neutral-light rounded-md">User '<EMAIL>' registered. Needs approval.</li>
            <li className="p-2 bg-neutral-light rounded-md">Clinic 'City Central Health' registration pending.</li>
            <li className="p-2 bg-neutral-light rounded-md">System backup completed successfully.</li>
          </ul>
        </Card>
        <Card title="System Health">
          <p className="text-neutral-DEFAULT">System status indicators will go here...</p>
          <div className="mt-4 space-y-2">
            <div className="flex items-center">
              <span className="h-3 w-3 bg-success rounded-full mr-2"></span> API Status: Operational
            </div>
            <div className="flex items-center">
              <span className="h-3 w-3 bg-success rounded-full mr-2"></span> Database: Connected
            </div>
            <div className="flex items-center">
              <span className="h-3 w-3 bg-warning rounded-full mr-2"></span> Background Jobs: Minor Delays
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default AdminDashboard;
Use code with caution.
Jsx
(Create similar placeholder dashboards for Doctor, Patient, and Clinic roles. They would fetch their specific dashboard data from their respective mock services.)
Next Steps:
Fill out src/main.jsx (or index.js): Ensure AuthProvider wraps the App.
// src/main.jsx (or index.js)
import React from 'react'
import ReactDOM from 'react-dom/client'
import App from './App.jsx'
import './styles/tailwind.css' // or your global css file
import { AuthProvider } from './context/AuthContext.jsx' // Ensure path is correct

ReactDOM.createRoot(document.getElementById('root')).render(
  <React.StrictMode>
    <AuthProvider> {/* Wrap App with AuthProvider */}
      <App />
    </AuthProvider>
  </React.StrictMode>,
)
Use code with caution.
JavaScript
Flesh out other components: Start with the common components, then move to role-specific ones.
Implement Forms: Create components in src/components/forms/ that match your DTOs. Use Input, Button, and other common components.
Mock Data Tables: For components like UserManagement.jsx, PatientList.jsx, etc., create a basic DataTable.jsx component in src/components/common/ and populate it with mock data from your services.
Refine UI/UX: Add more icons, improve spacing, ensure responsiveness, and add subtle animations/transitions.
Testing: Start thinking about testing (e.g., with Vitest or React Testing Library).
This is a substantial starting point. Run npm run dev to see the application in your browser. You should be able to navigate, log in (with mock credentials), and see the basic layout and placeholder pages. Remember to create all the empty files first to avoid import errors.
163.1s
ThinkingThoughts
(experimental)
Expand to view model thoughts

chevron_right
