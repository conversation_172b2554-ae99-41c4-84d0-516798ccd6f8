package com.example.MediConnect.validation;

import jakarta.validation.ConstraintValidator;
import jakarta.validation.ConstraintValidatorContext;

import java.util.regex.Pattern;

public class PhoneNumberValidator implements ConstraintValidator<ValidPhoneNumber, String> {
    
    private static final Pattern PHONE_PATTERN = Pattern.compile(
        "^[+]?[1-9]\\d{1,14}$|^[+]?[(]?[\\d\\s\\-\\(\\)]{10,}$"
    );
    
    @Override
    public void initialize(ValidPhoneNumber constraintAnnotation) {
        // Initialization logic if needed
    }
    
    @Override
    public boolean isValid(String phoneNumber, ConstraintValidatorContext context) {
        if (phoneNumber == null || phoneNumber.trim().isEmpty()) {
            return true; // Let @NotBlank handle null/empty validation
        }
        
        // Remove all spaces, dashes, and parentheses for validation
        String cleanedNumber = phoneNumber.replaceAll("[\\s\\-\\(\\)]", "");
        
        return PHONE_PATTERN.matcher(cleanedNumber).matches();
    }
}
