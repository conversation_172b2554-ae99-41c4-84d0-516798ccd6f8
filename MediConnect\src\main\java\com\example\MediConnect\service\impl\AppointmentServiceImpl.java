package com.example.MediConnect.service.impl;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import com.example.MediConnect.dto.AppointmentDTO;
import com.example.MediConnect.dto.request.AppointmentRequest;
import com.example.MediConnect.entity.Appointment;
import com.example.MediConnect.entity.Doctor;
import com.example.MediConnect.entity.Patient;
import com.example.MediConnect.enums.AppointmentStatus;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.AppointmentRepository;
import com.example.MediConnect.repository.DoctorRepository;
import com.example.MediConnect.repository.PatientRepository;
import com.example.MediConnect.service.AppointmentService;
import com.example.MediConnect.util.Constants;

@Service
@Transactional
public class AppointmentServiceImpl implements AppointmentService {

    @Autowired
    private AppointmentRepository appointmentRepository;

    @Autowired
    private DoctorRepository doctorRepository;

    @Autowired
    private PatientRepository patientRepository;

    @Override
    public AppointmentDTO createAppointment(AppointmentRequest appointmentRequest) {
        Doctor doctor = doctorRepository.findById(appointmentRequest.getDoctorId())
                .orElseThrow(() -> new ResourceNotFoundException(Constants.DOCTOR_NOT_FOUND));

        Patient patient = patientRepository.findById(appointmentRequest.getPatientId())
                .orElseThrow(() -> new ResourceNotFoundException(Constants.PATIENT_NOT_FOUND));

        // Check if slot is available
        if (!isSlotAvailable(appointmentRequest.getDoctorId(),
                appointmentRequest.getAppointmentDate(),
                appointmentRequest.getDurationMinutes())) {
            throw new IllegalArgumentException(Constants.APPOINTMENT_SLOT_NOT_AVAILABLE);
        }

        // Check if doctor has a clinic assigned
        if (doctor.getClinic() == null) {
            throw new IllegalArgumentException("Doctor must be assigned to a clinic to book appointments");
        }

        Appointment appointment = new Appointment();
        appointment.setDoctor(doctor);
        appointment.setPatient(patient);
        appointment.setClinic(doctor.getClinic());
        appointment.setAppointmentDate(appointmentRequest.getAppointmentDate());
        appointment.setReason(appointmentRequest.getReason());
        appointment.setNotes(appointmentRequest.getNotes());
        appointment.setDurationMinutes(appointmentRequest.getDurationMinutes() != null ?
                appointmentRequest.getDurationMinutes() : Constants.DEFAULT_APPOINTMENT_DURATION);
        appointment.setStatus(AppointmentStatus.SCHEDULED);

        appointment = appointmentRepository.save(appointment);
        return convertToDTO(appointment);
    }

    @Override
    public AppointmentDTO getAppointmentById(Long appointmentId) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.APPOINTMENT_NOT_FOUND));
        return convertToDTO(appointment);
    }

    @Override
    public AppointmentDTO updateAppointmentStatus(Long appointmentId, AppointmentStatus status) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.APPOINTMENT_NOT_FOUND));

        appointment.setStatus(status);
        appointment = appointmentRepository.save(appointment);
        return convertToDTO(appointment);
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByPatient(Long patientId) {
        List<Appointment> appointments = appointmentRepository.findByPatientId(patientId);
        return appointments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByDoctor(Long doctorId) {
        List<Appointment> appointments = appointmentRepository.findByDoctorId(doctorId);
        return appointments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByClinic(Long clinicId) {
        List<Appointment> appointments = appointmentRepository.findByClinicId(clinicId);
        return appointments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByDateRange(LocalDateTime startDate, LocalDateTime endDate) {
        List<Appointment> appointments = appointmentRepository.findByAppointmentDateBetween(startDate, endDate);
        return appointments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public List<AppointmentDTO> getAppointmentsByStatus(AppointmentStatus status) {
        List<Appointment> appointments = appointmentRepository.findByStatus(status);
        return appointments.stream()
                .map(this::convertToDTO)
                .collect(Collectors.toList());
    }

    @Override
    public void cancelAppointment(Long appointmentId, String reason) {
        Appointment appointment = appointmentRepository.findById(appointmentId)
                .orElseThrow(() -> new ResourceNotFoundException(Constants.APPOINTMENT_NOT_FOUND));

        appointment.setStatus(AppointmentStatus.CANCELLED);
        appointment.setCancelledAt(LocalDateTime.now());
        appointment.setCancellationReason(reason);
        appointmentRepository.save(appointment);
    }

    @Override
    public boolean isSlotAvailable(Long doctorId, LocalDateTime appointmentDate, Integer duration) {
        if (duration == null) {
            duration = Constants.DEFAULT_APPOINTMENT_DURATION;
        }

        LocalDateTime endTime = appointmentDate.plusMinutes(duration);

        List<Appointment> conflictingAppointments = appointmentRepository
                .findByDoctorIdAndAppointmentDateBetween(doctorId,
                        appointmentDate.minusMinutes(duration),
                        endTime);

        return conflictingAppointments.stream()
                .noneMatch(apt -> apt.getStatus() == AppointmentStatus.SCHEDULED ||
                                 apt.getStatus() == AppointmentStatus.CONFIRMED ||
                                 apt.getStatus() == AppointmentStatus.IN_PROGRESS);
    }

    @Override
    public List<String> getAvailableSlots(Long doctorId, String date) {
        // This is a simplified implementation
        // In a real application, you would check doctor's schedule and existing appointments
        return List.of("09:00", "09:30", "10:00", "10:30", "11:00", "11:30",
                      "14:00", "14:30", "15:00", "15:30", "16:00", "16:30");
    }

    private AppointmentDTO convertToDTO(Appointment appointment) {
        AppointmentDTO dto = new AppointmentDTO();
        dto.setId(appointment.getId());
        dto.setAppointmentDate(appointment.getAppointmentDate());
        dto.setStatus(appointment.getStatus());
        dto.setReason(appointment.getReason());
        dto.setNotes(appointment.getNotes());
        dto.setDurationMinutes(appointment.getDurationMinutes());
        dto.setCreatedAt(appointment.getCreatedAt());
        dto.setUpdatedAt(appointment.getUpdatedAt());
        dto.setCancellationReason(appointment.getCancellationReason());

        if (appointment.getPatient() != null) {
            dto.setPatientId(appointment.getPatient().getId());
            dto.setPatientName(appointment.getPatient().getName());
            dto.setPatientEmail(appointment.getPatient().getEmail());
        }

        if (appointment.getDoctor() != null) {
            dto.setDoctorId(appointment.getDoctor().getId());
            dto.setDoctorName(appointment.getDoctor().getName());
            if (appointment.getDoctor().getSpeciality() != null) {
                dto.setDoctorSpecialty(appointment.getDoctor().getSpeciality().getName().toString());
            }
        }

        if (appointment.getClinic() != null) {
            dto.setClinicId(appointment.getClinic().getId());
            dto.setClinicName(appointment.getClinic().getClinicName());
        }

        return dto;
    }
}
