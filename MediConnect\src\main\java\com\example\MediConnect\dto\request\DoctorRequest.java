package com.example.MediConnect.dto.request;

import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DoctorRequest {
    
    @NotBlank(message = "Name is required")
    private String name;
    
    @NotBlank(message = "Email is required")
    @Email(message = "Email should be valid")
    private String email;
    
    private String phoneNumber;
    private String address;
    private String dateOfBirth;
    private String gender;
    
    @NotBlank(message = "Medical license is required")
    private String medicalLicense;
    
    private Long specialityId;
    private Integer yearsOfExperience;
    private String qualification;
    private Double consultationFee;
    private String bio;
    private Long clinicId;
}
