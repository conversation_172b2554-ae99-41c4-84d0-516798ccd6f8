package com.example.MediConnect.repository;

import com.example.MediConnect.entity.Clinic;
import com.example.MediConnect.enums.ClinicStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ClinicRepository extends JpaRepository<Clinic, Long> {
    
    Optional<Clinic> findByEmail(String email);
    
    Optional<Clinic> findByLicenseNumber(String licenseNumber);
    
    boolean existsByLicenseNumber(String licenseNumber);
    
    List<Clinic> findByClinicStatus(ClinicStatus status);
    
    @Query("SELECT c FROM Clinic c WHERE c.clinicName LIKE %:name%")
    List<Clinic> findByClinicNameContaining(@Param("name") String name);
    
    @Query("SELECT COUNT(c) FROM Clinic c WHERE c.clinicStatus = :status")
    Long countByStatus(@Param("status") ClinicStatus status);
}
