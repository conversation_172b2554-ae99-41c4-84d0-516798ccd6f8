package com.example.MediConnect.dto.response;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class SystemReportResponse {
    private Long totalUsers;
    private Long totalDoctors;
    private Long totalClinics;
    private Long totalPatients;
    private Long totalAppointments;
    private Long pendingDoctorApprovals;
    private Long pendingClinicApprovals;
    private Long todayAppointments;
    private Long completedAppointments;
    private Long cancelledAppointments;
}
