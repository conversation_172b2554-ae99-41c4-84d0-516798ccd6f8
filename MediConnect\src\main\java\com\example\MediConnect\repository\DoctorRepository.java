package com.example.MediConnect.repository;

import com.example.MediConnect.entity.Doctor;
import com.example.MediConnect.entity.Speciality;
import com.example.MediConnect.enums.DoctorStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DoctorRepository extends JpaRepository<Doctor, Long> {
    
    Optional<Doctor> findByEmail(String email);
    
    Optional<Doctor> findByMedicalLicense(String medicalLicense);
    
    boolean existsByMedicalLicense(String medicalLicense);
    
    List<Doctor> findByDoctorStatus(DoctorStatus status);
    
    List<Doctor> findBySpeciality(Speciality speciality);
    
    List<Doctor> findByClinicId(Long clinicId);
    
    @Query("SELECT d FROM Doctor d WHERE d.speciality = :speciality AND d.doctorStatus = :status")
    List<Doctor> findBySpecialityAndStatus(@Param("speciality") Speciality speciality, @Param("status") DoctorStatus status);
    
    @Query("SELECT d FROM Doctor d WHERE d.clinic.id = :clinicId AND d.doctorStatus = :status")
    List<Doctor> findByClinicIdAndStatus(@Param("clinicId") Long clinicId, @Param("status") DoctorStatus status);
    
    @Query("SELECT COUNT(d) FROM Doctor d WHERE d.doctorStatus = :status")
    Long countByStatus(@Param("status") DoctorStatus status);
}
