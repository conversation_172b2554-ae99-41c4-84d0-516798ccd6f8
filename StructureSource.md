# MediConnect Frontend Structure 

## Project Structure Aligned with Backend Architecture

```
mediconnect frontend/
├── 📄 package.json                      # Dependencies and scripts
├── 📄 tailwind.config.js               # Tailwind CSS configuration
├── 📄 .env                             # Environment variables
├── 📄 .gitignore                       # Git ignore rules
├── 📄 README.md                        # Project documentation
│
├── 📁 public/
│   ├── 📄 index.html                   # Main HTML template
│   ├── 📄 favicon.ico                  # App favicon
│   └── 📄 manifest.json                # PWA manifest
│
└── 📁 src/
    ├── 📄 App.jsx                      # Main App component
    ├── 📄 index.js                     # App entry point
    ├── 📄 reportWebVitals.js           # Performance monitoring
    │
    ├── 📁 components/
    │   ├── 📁 common/                  # Reusable UI components
    │   │   ├── 📄 Header.jsx
    │   │   ├── 📄 Footer.jsx
    │   │   ├── 📄 Navbar.jsx
    │   │   ├── 📄 Sidebar.jsx
    │   │   ├── 📄 Loading.jsx
    │   │   ├── 📄 Modal.jsx
    │   │   ├── 📄 Button.jsx
    │   │   ├── 📄 Input.jsx
    │   │   ├── 📄 Select.jsx
    │   │   ├── 📄 Card.jsx
    │   │   ├── 📄 DataTable.jsx         # For listing data
    │   │   ├── 📄 StatusBadge.jsx       # For status indicators
    │   │   ├── 📄 DatePicker.jsx        # For date selection
    │   │   ├── 📄 TimePicker.jsx        # For time selection
    │   │   ├── 📄 SearchBar.jsx
    │   │   ├── 📄 Pagination.jsx        # For paginated lists
    │   │   ├── 📄 ConfirmDialog.jsx     # For confirmations
    │   │   ├── 📄 Toast.jsx             # For notifications
    │   │   ├── 📄 Avatar.jsx            # User avatars
    │   │   └── 📄 EmptyState.jsx        # Empty state component
    │   │
    │   ├── 📁 auth/                    # Authentication components
    │   │   ├── 📄 Login.jsx
    │   │   ├── 📄 Register.jsx
    │   │   ├── 📄 ForgotPassword.jsx    # Password recovery
    │   │   └── 📄 ResetPassword.jsx     # Password reset
    │   │
    │   ├── 📁 admin/                   # Admin role components (7 controllers)
    │   │   ├── 📄 AdminDashboard.jsx
    │   │   ├── 📄 UserManagement.jsx
    │   │   ├── 📄 DoctorApproval.jsx    # Doctor verification/approval
    │   │   ├── 📄 ClinicManagement.jsx  # Clinic oversight
    │   │   ├── 📄 SystemReports.jsx
    │   │   ├── 📄 SystemSettings.jsx    # System configuration
    │   │   ├── 📄 AnnouncementManager.jsx  # System-wide announcements
    │   │   ├── 📄 AuditLogs.jsx         # System audit trail
    │   │   └── 📄 AnalyticsDashboard.jsx   # Advanced analytics
    │   │
    │   ├── 📁 doctor/                  # Doctor role components
    │   │   ├── 📄 DoctorDashboard.jsx
    │   │   ├── 📄 DoctorProfile.jsx
    │   │   ├── 📄 PatientList.jsx
    │   │   ├── 📄 AppointmentManagement.jsx  # Doctor's appointments
    │   │   ├── 📄 DiagnosisEntry.jsx
    │   │   ├── 📄 PrescriptionManager.jsx    # Prescription management
    │   │   ├── 📄 FollowUpScheduling.jsx
    │   │   ├── 📄 ScheduleManagement.jsx     # Doctor availability
    │   │   ├── 📄 PatientHistory.jsx         # Patient medical history
    │   │   └── 📄 MedicalRecords.jsx         # Complete medical records
    │   │
    │   ├── 📁 clinic/                  # Clinic role components  
    │   │   ├── 📄 ClinicDashboard.jsx
    │   │   ├── 📄 ClinicProfile.jsx          # Clinic information
    │   │   ├── 📄 StaffManagement.jsx        # Clinic staff management
    │   │   ├── 📄 DoctorAssignment.jsx       # Assign doctors to clinic
    │   │   ├── 📄 AppointmentOverview.jsx
    │   │   ├── 📄 ScheduleCoordination.jsx   # Coordinate doctor schedules
    │   │   ├── 📄 AnnouncementBoard.jsx
    │   │   ├── 📄 ClinicReports.jsx          # Clinic-specific reports
    │   │   └── 📄 ResourceManagement.jsx     # Clinic resources
    │   │
    │   ├── 📁 patient/                 # Patient role components
    │   │   ├── 📄 PatientDashboard.jsx
    │   │   ├── 📄 PatientProfile.jsx         # Patient information
    │   │   ├── 📄 BookAppointment.jsx
    │   │   ├── 📄 DoctorSearch.jsx
    │   │   ├── 📄 AppointmentHistory.jsx     # Past appointments
    │   │   ├── 📄 MedicalHistory.jsx
    │   │   ├── 📄 PrescriptionHistory.jsx    # Prescription records
    │   │   ├── 📄 DiagnosisHistory.jsx       # Diagnosis records
    │   │   ├── 📄 FollowUpTracker.jsx        # Track follow-ups
    │   │   ├── 📄 ClinicDirectory.jsx        # Find clinics
    │   │   └── 📄 HealthRecords.jsx          # Personal health records
    │   │
    │   ├── 📁 shared/                  # Shared business components
    │   │   ├── 📄 AppointmentCard.jsx        # Appointment display
    │   │   ├── 📄 DiagnosisCard.jsx          # Diagnosis display
    │   │   ├── 📄 PrescriptionCard.jsx       # Prescription display
    │   │   ├── 📄 FollowUpCard.jsx           # Follow-up display
    │   │   ├── 📄 DoctorCard.jsx             # Doctor information card
    │   │   ├── 📄 ClinicCard.jsx             # Clinic information card
    │   │   ├── 📄 PatientCard.jsx            # Patient information card
    │   │   ├── 📄 SpecialtyFilter.jsx        # Filter by medical specialty
    │   │   ├── 📄 StatusFilter.jsx           # Filter by status
    │   │   └── 📄 NotificationBell.jsx       # Notification indicator
    │   │
    │   ├── 📁 forms/                   # Form components (aligned with DTOs)
    │   │   ├── 📄 AppointmentForm.jsx        # AppointmentRequest DTO
    │   │   ├── 📄 DiagnosisForm.jsx          # DiagnosisRequest DTO
    │   │   ├── 📄 DoctorRegistrationForm.jsx # DoctorRequest DTO
    │   │   ├── 📄 FollowUpForm.jsx           # FollowUpRequest DTO
    │   │   ├── 📄 AnnouncementForm.jsx       # AnnouncementRequest DTO
    │   │   ├── 📄 PrescriptionForm.jsx       # For prescription creation
    │   │   ├── 📄 PatientRegistrationForm.jsx # Patient registration
    │   │   ├── 📄 ClinicRegistrationForm.jsx # Clinic registration
    │   │   └── 📄 ScheduleForm.jsx           # Doctor schedule form
    │   │
    │   └── 📁 layout/                  # Layout components
    │       ├── 📄 Layout.jsx
    │       ├── 📄 ProtectedRoute.jsx
    │       ├── 📄 RoleBasedRoute.jsx
    │       ├── 📄 AdminLayout.jsx            # Admin-specific layout
    │       ├── 📄 DoctorLayout.jsx           # Doctor-specific layout
    │       ├── 📄 ClinicLayout.jsx           # Clinic-specific layout
    │       └── 📄 PatientLayout.jsx          # Patient-specific layout
    │
    ├── 📁 pages/                       # Page components
    │   ├── 📄 Home.jsx
    │   ├── 📄 About.jsx
    │   ├── 📄 Contact.jsx
    │   ├── 📄 Services.jsx                   # Healthcare services info
    │   ├── 📄 DoctorDirectory.jsx            # Public doctor directory
    │   ├── 📄 ClinicDirectory.jsx            # Public clinic directory
    │   ├── 📄 NotFound.jsx
    │   ├── 📄 Unauthorized.jsx
    │   ├── 📄 ServerError.jsx                # 500 error page
    │   └── 📄 Maintenance.jsx                # Maintenance mode page
    │
    ├── 📁 hooks/                       # Custom React hooks
    │   ├── 📄 useAuth.js
    │   ├── 📄 useApi.js
    │   ├── 📄 useLocalStorage.js
    │   ├── 📄 useNotifications.js            # Notification management
    │   ├── 📄 usePagination.js               # Pagination logic
    │   ├── 📄 useSearch.js                   # Search functionality
    │   ├── 📄 useForm.js                     # Form management
    │   ├── 📄 useAppointments.js             # Appointment operations
    │   ├── 📄 useDoctors.js                  # Doctor operations
    │   ├── 📄 usePatients.js                 # Patient operations
    │   ├── 📄 useClinics.js                  # Clinic operations
    │   └── 📄 useWebSocket.js                # Real-time notifications
    │
    ├── 📁 context/                     # React Context providers
    │   ├── 📄 AuthContext.jsx
    │   ├── 📄 NotificationContext.jsx
    │   ├── 📄 ThemeContext.jsx               # Theme management
    │   └── 📄 AppContext.jsx                 # Global app state
    │
    ├── 📁 services/                    # API service layer (aligned with backend)
    │   ├── 📄 api.js                         # Base API configuration
    │   ├── 📄 authService.js                 # AuthController endpoints
    │   ├── 📄 adminService.js                # AdminController endpoints
    │   ├── 📄 doctorService.js               # DoctorController endpoints
    │   ├── 📄 clinicService.js               # ClinicController endpoints
    │   ├── 📄 patientService.js              # PatientController endpoints
    │   ├── 📄 appointmentService.js          # AppointmentController endpoints
    │   ├── 📄 diagnosisService.js            # Diagnosis operations
    │   ├── 📄 prescriptionService.js         # Prescription operations
    │   ├── 📄 followUpService.js             # Follow-up operations
    │   ├── 📄 announcementService.js         # Announcement operations
    │   ├── 📄 specialtyService.js            # Specialty operations
    │   ├── 📄 scheduleService.js             # Doctor schedule operations
    │   ├── 📄 reportService.js               # Reporting operations
    │   ├── 📄 notificationService.js         # Notification operations
    │   └── 📄 uploadService.js               # File upload operations
    │
    ├── 📁 utils/                       # Utility functions
    │   ├── 📄 constants.js                   # App constants (aligned with backend)
    │   ├── 📄 helpers.js                     # General helper functions
    │   ├── 📄 validation.js                  # Frontend validation rules
    │   ├── 📄 dateUtils.js                   # Date manipulation utilities
    │   ├── 📄 formatters.js                  # Data formatting utilities
    │   ├── 📄 errorHandler.js                # Error handling utilities
    │   ├── 📄 permissions.js                 # Role-based permissions
    │   ├── 📄 statusUtils.js                 # Status mapping utilities
    │   └── 📄 apiUtils.js                    # API utility functions
    │
    ├── 📁 types/                       # TypeScript type definitions (if using TS)
    │   ├── 📄 auth.types.js
    │   ├── 📄 user.types.js
    │   ├── 📄 appointment.types.js
    │   ├── 📄 doctor.types.js
    │   ├── 📄 clinic.types.js
    │   ├── 📄 patient.types.js
    │   ├── 📄 diagnosis.types.js
    │   ├── 📄 prescription.types.js
    │   ├── 📄 followUp.types.js
    │   └── 📄 api.types.js
    │
    ├── 📁 styles/                      # Styling files
    │   ├── 📄 globals.css                    # Global styles
    │   ├── 📄 tailwind.css                   # Tailwind imports
    │   ├── 📄 components.css                 # Component-specific styles
    │   └── 📄 animations.css                 # Animation definitions
    │
    └── 📁 assets/                      # Static assets
        ├── 📁 images/
        │   ├── 📄 logo.png
        │   ├── 📄 hero-banner.jpg
        │   ├── 📄 placeholder-avatar.png
        │   └── 📁 specialties/               # Medical specialty icons
        ├── 📁 icons/
        │   ├── 📄 medical-icons.svg
        │   └── 📄 ui-icons.svg
        └── 📁 documents/
            ├── 📄 privacy-policy.md
            ├── 📄 terms-of-service.md
            └── 📄 user-guide.pdf
```

## 📊 Alignment with Backend Structure

### **Controller-Service Mapping**
- **7 Backend Controllers** → **7 Frontend Services**
- **13 Backend Entities** → **Corresponding Frontend Components & Types**
- **18+ Backend DTOs** → **Aligned Frontend Forms & Data Structures**
- **9 Backend Enums** → **Frontend Constants & Validation Rules**

### **Role-Based Architecture**
- **Admin Components**: System management, user approval, reports
- **Doctor Components**: Patient care, diagnosis, prescriptions, schedules  
- **Clinic Components**: Staff management, resource coordination
- **Patient Components**: Appointments, medical history, doctor search

### **Enhanced Features Added**
- **Real-time Notifications**: WebSocket integration for live updates
- **Advanced Search & Filtering**: Enhanced user experience
- **Comprehensive Forms**: Aligned with backend request DTOs
- **File Upload Support**: For medical documents and images
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Error Handling**: Comprehensive error boundaries and user feedback
- **Accessibility**: WCAG compliant components
- **Performance**: Lazy loading and code splitting

### **Security Integration**  
- **JWT Token Management**: Secure authentication flow
- **Role-Based Access Control**: Component-level permission checks
- **Protected Routes**: Route guards based on user roles
- **API Security**: Request/response interceptors

### **Development Experience**
- **Hot Reloading**: Fast development cycle
- **Environment Configuration**: Multiple environment support
- **Linting & Formatting**: Code quality enforcement  
- **Testing Setup**: Unit and integration test foundation

This structure provides a robust, scalable frontend that perfectly complements the sophisticated Spring Boot backend architecture while delivering an exceptional user experience for all healthcare stakeholders.


COMPLETE MEDICONNECT API ENDPOINTS CATALOG
🔐 Authentication Endpoints (No auth required)
Method	Endpoint	Description	Status
POST	/api/auth/login	User login - Returns JWT token	
POST	/api/auth/register	User registration	
POST	/api/auth/logout	User logout	
POST	/api/auth/validate	Validate JWT token
POST	/api/auth/refresh	Refresh JWT token	
👑 Admin Endpoints (Requires ADMIN role)
Method	Endpoint	Description	Status
GET	/api/admin/clinics/pending	Get pending clinic approvals	
PUT	/api/admin/clinics/{id}/approve	Approve clinic	
PUT	/api/admin/clinics/{id}/reject	Reject clinic	
GET	/api/admin/doctors/pending	Get pending doctor approvals	
PUT	/api/admin/doctors/{id}/approve	Approve doctor	
PUT	/api/admin/doctors/{id}/reject	Reject doctor
GET	/api/admin/users	Get all users	
GET	/api/admin/users/{id}	Get user by ID	
PUT	/api/admin/users/{id}/activate	Activate user	
PUT	/api/admin/users/{id}/deactivate	Deactivate user	
GET	/api/admin/users/status/{status}	Get users by status	
GET	/api/admin/reports/overview	System overview statistics	
GET	/api/admin/reports/users	User statistics	
GET	/api/admin/reports/appointments	Appointment statistics	
POST	/api/admin/initialize-sample-data	Initialize sample data	
👨‍⚕️ Doctor Endpoints (Requires DOCTOR role)
Method	Endpoint	Description	Status
GET	/api/doctors/profile	Get doctor profile	
PUT	/api/doctors/profile	Update doctor profile	
GET	/api/doctors/patients	Get assigned patients	
GET	/api/doctors/patients/{id}	Get patient details	
POST	/api/doctors/patients/{id}/diagnosis	Log patient diagnosis	
GET	/api/doctors/patients/{id}/history	Get patient medical history	
POST	/api/doctors/followups	Schedule follow-up	
GET	/api/doctors/followups	Get scheduled follow-ups	
PUT	/api/doctors/followups/{id}	Update follow-up	
DELETE	/api/doctors/followups/{id}	Cancel follow-up	
GET	/api/doctors/dashboard	Doctor dashboard statistics	
🏥 Clinic Endpoints (Requires CLINIC role)
Method	Endpoint	Description	Status
GET	/api/clinics/profile	Get clinic profile	
PUT	/api/clinics/profile	Update clinic profile	
GET	/api/clinics/staff	Get clinic staff	
POST	/api/clinics/staff/doctors	Add doctor to clinic	
DELETE	/api/clinics/staff/doctors/{id}	Remove doctor from clinic	
POST	/api/clinics/staff/staff	Add clinic staff	
DELETE	/api/clinics/staff/staff/{id}	Remove clinic staff	
GET	/api/clinics/appointments	Get clinic appointments	
GET	/api/clinics/appointments/upcoming	Get upcoming appointments	
GET	/api/clinics/appointments/today	Get today's appointments	
GET	/api/clinics/announcements	Get clinic announcements	
POST	/api/clinics/announcements	Create announcement	
PUT	/api/clinics/announcements/{id}	Update announcement	
DELETE	/api/clinics/announcements/{id}	Delete announcement	
GET	/api/clinics/dashboard	Clinic dashboard statistics	
🤒 Patient Endpoints (Requires PATIENT role)
Method	Endpoint	Description	Status
GET	/api/patients/profile	Get patient profile	
PUT	/api/patients/profile	Update patient profile	
POST	/api/patients/appointments	Book appointment	
GET	/api/patients/appointments	Get patient appointments	
GET	/api/patients/appointments/history	Get appointment history	
PUT	/api/patients/appointments/{id}/cancel	Cancel appointment	
GET	/api/patients/search/doctors	Search doctors by specialty	
GET	/api/patients/search/clinics	Search clinics by specialty	
GET	/api/patients/doctors/{id}/availability	Get doctor availability	
GET	/api/patients/medical-history	Get medical history	
GET	/api/patients/diagnoses	Get diagnoses	
GET	/api/patients/prescriptions	Get prescriptions	
📅 Appointment Endpoints (Requires authentication)
Method	Endpoint	Description	Status
POST	/api/appointments	Create appointment	
GET	/api/appointments/{id}	Get appointment by ID	
PUT	/api/appointments/{id}/status	Update appointment status	
GET	/api/appointments/patient/{patientId}	Get appointments by patient	
GET	/api/appointments/doctor/{doctorId}	Get appointments by doctor	
GET	/api/appointments/clinic/{clinicId}	Get appointments by clinic	
GET	/api/appointments/date-range	Get appointments by date range	
GET	/api/appointments/status/{status}	Get appointments by status	
PUT	/api/appointments/{id}/cancel	Cancel appointment	
GET	/api/appointments/doctor/{doctorId}/availability	Get doctor availability	
GET	/api/appointments/check-availability	Check slot availability	


Comple Api endpoints with their Request body  and response body
🔧 SETUP INSTRUCTIONS
Base URL: http://localhost:8083/api

🔐 1. AUTHENTICATION ENDPOINTS
1.1 POST /api/auth/login
// Request Body
{
  "email": "<EMAIL>",
  "password": "test123"
}

// Response Body
{
  "token": "eyJhbGciOiJIUzI1NiJ9.**********************************************************************************************************************.qsXYMCD5ltnZvOyD0eB3V_mwLxoyZhtM6xDq0kP37e0",
  "email": "<EMAIL>",
  "role": "ADMIN",
  "userId": 2,
  "name": "Test Admin",
  "message": "Authentication successful"
}
// Request Body - Patient Registration
{
  "name": "John Doe",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+**********",
  "address": "123 Main St, City, State",
  "dateOfBirth": "1990-01-01",
  "gender": "MALE",
  "role": "PATIENT",
  "emergencyContactName": "Jane Doe",
  "emergencyContactPhone": "+**********",
  "bloodGroup": "O+",
  "allergies": "None",
  "medicalHistory": "No significant history"
}
1.2 POST /api/auth/register
// Request Body - Doctor Registration
{
  "name": "Dr. Jane Smith",
  "email": "<EMAIL>",
  "password": "password123",
  "phoneNumber": "+**********",
  "address": "456 Medical Ave, City, State",
  "dateOfBirth": "1985-05-15",
  "gender": "FEMALE",
  "role": "DOCTOR",
  "medicalLicense": "MD789012",
  "yearsOfExperience": 10,
  "qualification": "MBBS, MD",
  "consultationFee": 150.0,
  "bio": "Experienced family physician"
}

// Response Body
{
  "token": "jwt_token_here",
  "email": "<EMAIL>",
  "role": "PATIENT",
  "userId": 123,
  "name": "John Doe",
  "message": "Registration successful"
}
1.3 POST /api/auth/logout
// Headers
Authorization: Bearer {{adminToken}}

// Response Body
{
  "success": true,
  "message": "Logout successful",
  "data": null
}
1.4 POST /api/auth/validate
// Headers
Authorization: Bearer {{adminToken}}

// Response Body
{
  "success": true,
  "message": "Token is valid",
  "data": null
}
1.5 POST /api/auth/refresh
// Headers
Authorization: Bearer {{adminToken}}

// Response Body
{
  "success": true,
  "message": "Token refreshed successfully",
  "data": "new_jwt_token_here"
}
👑 2. ADMIN ENDPOINTS
2.1 GET /api/admin/reports/overview
// Headers
Authorization: Bearer {{adminToken}}

// Response Body
{
  "totalUsers": 18,
  "totalDoctors": 6,
  "totalClinics": 3,
  "totalPatients": 7,
  "totalAppointments": 6,
  "pendingDoctorApprovals": 0,
  "pendingClinicApprovals": 0,
  "todayAppointments": null,
  "completedAppointments": 1,
  "cancelledAppointments": 0
}
2.2 GET /api/admin/users
// Headers
Authorization: Bearer {{adminToken}}

// Response Body
[
  {
    "id": 2,
    "name": "Test Admin",
    "email": "<EMAIL>",
    "phoneNumber": "+**********",
    "address": "123 Admin St",
    "dateOfBirth": "1980-01-01",
    "gender": "MALE",
    "role": "ADMIN",
    "status": "ACTIVE",
    "createdAt": "2025-05-24T13:15:47.123456",
    "updatedAt": "2025-05-25T00:17:17.367497",
    "lastLogin": "2025-05-25T00:29:29.123456"
  }
]
2.3 PUT /api/admin/doctors/{id}/approve
// Headers
Authorization: Bearer {{adminToken}}

// URL: /api/admin/doctors/4/approve

// Response Body
{
  "success": true,
  "message": "Doctor approved successfully",
  "data": null
}
2.4 GET /api/admin/doctors/pending
// Headers
Authorization: Bearer {{adminToken}}

// Response Body
[
  {
    "id": 4,
    "name": "Dr. Sarah Wilson",
    "email": "<EMAIL>",
    "phoneNumber": "+**********",
    "address": "123 Medical St",
    "dateOfBirth": "1985-01-01",
    "gender": "FEMALE",
    "medicalLicense": "MD123456",
    "specialtyName": null,
    "yearsOfExperience": 8,
    "qualification": "MBBS, MD",
    "doctorStatus": "PENDING_APPROVAL",
    "clinicId": null,
    "clinicName": null,
    "consultationFee": 120.0,
    "bio": "Experienced general practitioner",
    "createdAt": "2025-05-24T14:01:47.87481",
    "updatedAt": "2025-05-25T00:07:19.757495"
  }
]
2.5 POST /api/admin/initialize-sample-data
// Headers
Authorization: Bearer {{adminToken}}

// Response Body
{
  "success": true,
  "message": "Sample data initialized successfully",
  "data": null
}
👨‍⚕️ 3. DOCTOR ENDPOINTS
3.1 GET /api/doctors/profile
// Headers
Authorization: Bearer {{doctorToken}}

// URL: /api/doctors/profile?doctorId=10

// Response Body
{
  "id": 10,
  "name": "Dr. Sarah Johnson",
  "email": "<EMAIL>",
  "phoneNumber": "******-1001",
  "address": "Medical District, MC",
  "dateOfBirth": "1985-03-20",
  "gender": "FEMALE",
  "medicalLicense": "MD12345",
  "specialtyName": "CARDIOLOGY",
  "yearsOfExperience": 12,
  "qualification": "MBBS, MD Cardiology",
  "doctorStatus": "ACTIVE",
  "clinicId": 7,
  "clinicName": "MediConnect Central Clinic",
  "consultationFee": 150.0,
  "bio": "Experienced cardiologist specializing in heart disease prevention",
  "createdAt": "2025-05-25T00:13:40.123456",
  "updatedAt": "2025-05-25T00:13:40.123456"
}
3.2 GET /api/doctors/patients
// Headers
Authorization: Bearer {{doctorToken}}

// URL: /api/doctors/patients?doctorId=10

// Response Body
[
  {
    "id": 15,
    "name": "John Smith",
    "email": "<EMAIL>",
    "phoneNumber": "******-2001",
    "address": "Patient District, MC",
    "dateOfBirth": "1990-04-15",
    "gender": "MALE",
    "patientId": "PAT001",
    "emergencyContactName": "Jane Smith",
    "emergencyContactPhone": "******-2002",
    "bloodGroup": "O+",
    "allergies": "None",
    "medicalHistory": "No significant history",
    "insuranceProvider": null,
    "insuranceNumber": null,
    "createdAt": "2025-05-25T00:13:40.706653",
    "updatedAt": "2025-05-25T00:13:40.706653"
  }
]
3.3 POST /api/doctors/patients/{id}/diagnosis
// Headers
Authorization: Bearer {{doctorToken}}
Content-Type: application/json

// URL: /api/doctors/patients/15/diagnosis

// Request Body
{
  "doctorId": 10,
  "appointmentId": 6,
  "diagnosisCode": "I10",
  "diagnosisDescription": "Essential hypertension",
  "symptoms": "High blood pressure, headache, dizziness",
  "treatmentPlan": "Lifestyle changes, medication, regular monitoring",
  "doctorNotes": "Patient shows signs of mild hypertension. Recommend dietary changes and exercise."
}

// Response Body
{
  "id": 2,
  "patientId": 15,
  "patientName": "John Smith",
  "doctorId": 10,
  "doctorName": "Dr. Sarah Johnson",
  "appointmentId": 6,
  "diagnosisCode": "I10",
  "diagnosisDescription": "Essential hypertension",
  "symptoms": "High blood pressure, headache, dizziness",
  "treatmentPlan": "Lifestyle changes, medication, regular monitoring",
  "doctorNotes": "Patient shows signs of mild hypertension. Recommend dietary changes and exercise.",
  "createdAt": "2025-05-25T01:00:00.123456",
  "updatedAt": "2025-05-25T01:00:00.123456"
}
3.4 POST /api/doctors/followups
// Headers
Authorization: Bearer {{doctorToken}}
Content-Type: application/json

// Request Body
{
  "patientId": 15,
  "doctorId": 10,
  "scheduledDate": "2025-06-15T10:00:00",
  "reason": "Blood pressure follow-up",
  "notes": "Check medication effectiveness and blood pressure readings"
}

// Response Body
{
  "id": 3,
  "patientId": 15,
  "patientName": "John Smith",
  "doctorId": 10,
  "doctorName": "Dr. Sarah Johnson",
  "scheduledDate": "2025-06-15T10:00:00",
  "status": "SCHEDULED",
  "reason": "Blood pressure follow-up",
  "notes": "Check medication effectiveness and blood pressure readings",
  "createdAt": "2025-05-25T01:05:00.123456",
  "updatedAt": "2025-05-25T01:05:00.123456"
}
3.5 GET /api/doctors/dashboard
// Headers
Authorization: Bearer {{doctorToken}}

// URL: /api/doctors/dashboard?doctorId=10

// Response Body
{
  "doctorId": 10,
  "totalPatients": 2,
  "upcomingFollowUps": 1,
  "todayAppointments": null,
  "completedDiagnoses": null
}
🏥 4. CLINIC ENDPOINTS
4.1 GET /api/clinics/profile
// Headers
Authorization: Bearer {{clinicToken}}

// URL: /api/clinics/profile?clinicId=7

// Response Body
{
  "id": 7,
  "name": "MediConnect Central Clinic",
  "email": "<EMAIL>",
  "phoneNumber": "******-0100",
  "address": "456 Central Avenue, Medical District, MC 12346",
  "clinicName": "MediConnect Central Clinic",
  "licenseNumber": null,
  "description": "Premier healthcare facility with state-of-the-art equipment",
  "clinicStatus": "ACTIVE",
  "operatingHours": "Mon-Fri: 8:00 AM - 8:00 PM, Sat-Sun: 9:00 AM - 5:00 PM",
  "emergencyContact": "******-0199",
  "websiteUrl": "https://central.mediconnect.com",
  "createdAt": "2025-05-25T00:13:39.396137",
  "updatedAt": "2025-05-25T00:25:41.928355",
  "totalDoctors": 2,
  "totalStaff": 0
}
4.2 GET /api/clinics/appointments
// Headers
Authorization: Bearer {{clinicToken}}

// URL: /api/clinics/appointments?clinicId=7

// Response Body
[
  {
    "id": 6,
    "patientId": 3,
    "patientName": "John Patient",
    "patientEmail": "<EMAIL>",
    "doctorId": 10,
    "doctorName": "Dr. Sarah Johnson",
    "doctorSpecialty": "CARDIOLOGY",
    "clinicId": 7,
    "clinicName": "MediConnect Central Clinic",
    "appointmentDate": "2025-05-27T14:00:00",
    "status": "CONFIRMED",
    "reason": "Regular checkup",
    "notes": null,
    "durationMinutes": 30,
    "createdAt": "2025-05-25T00:23:51.789458",
    "updatedAt": "2025-05-25T00:23:51.789458",
    "cancellationReason": null
  }
]
4.3 POST /api/clinics/announcements
// Headers
Authorization: Bearer {{clinicToken}}
Content-Type: application/json

// URL: /api/clinics/announcements?clinicId=7

// Request Body
{
  "title": "New Operating Hours",
  "content": "We are extending our operating hours to better serve our patients.",
  "priority": "MEDIUM",
  "startDate": "2025-05-25T00:00:00",
  "endDate": "2025-06-25T23:59:59"
}

// Response Body
{
  "id": 4,
  "clinicId": 7,
  "clinicName": "MediConnect Central Clinic",
  "title": "New Operating Hours",
  "content": "We are extending our operating hours to better serve our patients.",
  "priority": "MEDIUM",
  "isActive": true,
  "startDate": "2025-05-25T00:00:00",
  "endDate": "2025-06-25T23:59:59",
  "createdAt": "2025-05-25T01:10:00.123456",
  "updatedAt": "2025-05-25T01:10:00.123456"
}
4.4 GET /api/clinics/dashboard
// Headers
Authorization: Bearer {{clinicToken}}

// URL: /api/clinics/dashboard?clinicId=7

// Response Body
{
  "clinicId": 7,
  "totalStaff": 0,
  "totalDoctors": null,
  "totalAppointments": 1,
  "todayAppointments": 0,
  "upcomingAppointments": 1,
  "activeAnnouncements": 2
}
🤒 5. PATIENT ENDPOINTS
5.1 GET /api/patients/profile
// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/patients/profile?patientId=3

// Response Body
{
  "id": 3,
  "name": "John Patient",
  "email": "<EMAIL>",
  "phoneNumber": null,
  "address": null,
  "dateOfBirth": null,
  "gender": null,
  "patientId": "PAT1748081762041",
  "emergencyContactName": "Jane Patient",
  "emergencyContactPhone": "+**********",
  "bloodGroup": "O+",
  "allergies": null,
  "medicalHistory": null,
  "insuranceProvider": null,
  "insuranceNumber": null,
  "createdAt": "2025-05-24T13:16:02.045751",
  "updatedAt": "2025-05-25T00:17:17.367497"
}
5.2 PUT /api/patients/profile
// Headers
Authorization: Bearer {{patientToken}}
Content-Type: application/json

// URL: /api/patients/profile?patientId=3

// Request Body
{
  "name": "John Patient Updated",
  "phoneNumber": "+**********",
  "address": "456 Patient Ave, City, State",
  "dateOfBirth": "1990-05-15",
  "gender": "MALE",
  "emergencyContactName": "Jane Patient",
  "emergencyContactPhone": "+**********",
  "bloodGroup": "O+",
  "allergies": "Peanuts",
  "medicalHistory": "Hypertension, Diabetes",
  "insuranceProvider": "HealthCare Plus",
  "insuranceNumber": "HC123456789"
}

// Response Body
{
  "id": 3,
  "name": "John Patient Updated",
  "email": "<EMAIL>",
  "phoneNumber": "+**********",
  "address": "456 Patient Ave, City, State",
  "dateOfBirth": "1990-05-15",
  "gender": "MALE",
  "patientId": "PAT1748081762041",
  "emergencyContactName": "Jane Patient",
  "emergencyContactPhone": "+**********",
  "bloodGroup": "O+",
  "allergies": "Peanuts",
  "medicalHistory": "Hypertension, Diabetes",
  "insuranceProvider": "HealthCare Plus",
  "insuranceNumber": "HC123456789",
  "createdAt": "2025-05-24T13:16:02.045751",
  "updatedAt": "2025-05-25T01:30:00.123456"
}

5.3 POST /api/patients/appointments

// Headers
Authorization: Bearer {{patientToken}}
Content-Type: application/json

// Request Body
{
  "patientId": 3,
  "doctorId": 10,
  "appointmentDate": "2025-05-28T09:00:00",
  "reason": "Follow-up consultation",
  "notes": "Patient experiencing mild symptoms",
  "durationMinutes": 30
}

// Response Body
{
  "id": 7,
  "patientId": 3,
  "patientName": "John Patient",
  "patientEmail": "<EMAIL>",
  "doctorId": 10,
  "doctorName": "Dr. Sarah Johnson",
  "doctorSpecialty": "CARDIOLOGY",
  "clinicId": 7,
  "clinicName": "MediConnect Central Clinic",
  "appointmentDate": "2025-05-28T09:00:00",
  "status": "SCHEDULED",
  "reason": "Follow-up consultation",
  "notes": "Patient experiencing mild symptoms",
  "durationMinutes": 30,
  "createdAt": "2025-05-25T01:35:00.123456",
  "updatedAt": "2025-05-25T01:35:00.123456",
  "cancellationReason": null
}
5.4 GET /api/patients/appointments

// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/patients/appointments?patientId=3

// Response Body
[
  {
    "id": 6,
    "patientId": 3,
    "patientName": "John Patient",
    "patientEmail": "<EMAIL>",
    "doctorId": 10,
    "doctorName": "Dr. Sarah Johnson",
    "doctorSpecialty": "CARDIOLOGY",
    "clinicId": 7,
    "clinicName": "MediConnect Central Clinic",
    "appointmentDate": "2025-05-27T14:00:00",
    "status": "CONFIRMED",
    "reason": "Regular checkup",
    "notes": null,
    "durationMinutes": 30,
    "createdAt": "2025-05-25T00:23:51.789458",
    "updatedAt": "2025-05-25T00:23:51.789458",
    "cancellationReason": null
  }
]
5.5 GET /api/patients/search/doctors

// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/patients/search/doctors?specialty=CARDIOLOGY

// Response Body
[
  {
    "id": 10,
    "name": "Dr. Sarah Johnson",
    "email": "<EMAIL>",
    "phoneNumber": "******-1001",
    "address": "Medical District, MC",
    "dateOfBirth": "1985-03-20",
    "gender": "FEMALE",
    "medicalLicense": "MD12345",
    "specialtyName": "CARDIOLOGY",
    "yearsOfExperience": 12,
    "qualification": "MBBS, MD Cardiology",
    "doctorStatus": "ACTIVE",
    "clinicId": 7,
    "clinicName": "MediConnect Central Clinic",
    "consultationFee": 150.0,
    "bio": "Experienced cardiologist specializing in heart disease prevention",
    "createdAt": "2025-05-25T00:13:40.123456",
    "updatedAt": "2025-05-25T00:13:40.123456"
  }
]
5.6 GET /api/patients/doctors/{id}/availability

// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/patients/doctors/10/availability?date=2025-05-28

// Response Body
[
  "09:00",
  "09:30",
  "10:00",
  "10:30",
  "11:00",
  "11:30",
  "14:00",
  "14:30",
  "15:00",
  "15:30",
  "16:00",
  "16:30"
]
5.7 GET /api/patients/medical-history

// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/patients/medical-history?patientId=3

// Response Body
[
  {
    "id": 1,
    "patientId": 3,
    "patientName": "John Patient",
    "doctorId": 10,
    "doctorName": "Dr. Sarah Johnson",
    "appointmentId": 6,
    "diagnosisCode": "I10",
    "diagnosisDescription": "Essential hypertension",
    "symptoms": "High blood pressure, headache, dizziness",
    "treatmentPlan": "Lifestyle changes, medication, regular monitoring",
    "doctorNotes": "Patient shows signs of mild hypertension. Recommend dietary changes and exercise.",
    "createdAt": "2025-05-25T01:00:00.123456",
    "updatedAt": "2025-05-25T01:00:00.123456"
  }
]
5.8 PUT /api/patients/appointments/{id}/cancel

// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/patients/appointments/6/cancel

// Response Body
{
  "success": true,
  "message": "Appointment cancelled successfully",
  "data": null
}
📅 6. APPOINTMENT ENDPOINTS
6.1 GET /api/appointments/patient/{patientId}
// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/appointments/patient/3

// Response Body
[
  {
    "id": 6,
    "patientId": 3,
    "patientName": "John Patient",
    "patientEmail": "<EMAIL>",
    "doctorId": 10,
    "doctorName": "Dr. Sarah Johnson",
    "doctorSpecialty": "CARDIOLOGY",
    "clinicId": 7,
    "clinicName": "MediConnect Central Clinic",
    "appointmentDate": "2025-05-27T14:00:00",
    "status": "CONFIRMED",
    "reason": "Regular checkup",
    "notes": null,
    "durationMinutes": 30,
    "createdAt": "2025-05-25T00:23:51.789458",
    "updatedAt": "2025-05-25T00:23:51.789458",
    "cancellationReason": null
  }
]
6.2 GET /api/appointments/doctor/{doctorId}
// Headers
Authorization: Bearer {{doctorToken}}

// URL: /api/appointments/doctor/10

// Response Body
[
  {
    "id": 6,
    "patientId": 3,
    "patientName": "John Patient",
    "patientEmail": "<EMAIL>",
    "doctorId": 10,
    "doctorName": "Dr. Sarah Johnson",
    "doctorSpecialty": "CARDIOLOGY",
    "clinicId": 7,
    "clinicName": "MediConnect Central Clinic",
    "appointmentDate": "2025-05-27T14:00:00",
    "status": "CONFIRMED",
    "reason": "Regular checkup",
    "notes": null,
    "durationMinutes": 30,
    "createdAt": "2025-05-25T00:23:51.789458",
    "updatedAt": "2025-05-25T00:23:51.789458",
    "cancellationReason": null
  }
]
6.3 GET /api/appointments/check-availability
// Headers
Authorization: Bearer {{patientToken}}

// URL: /api/appointments/check-availability?doctorId=10&appointmentDate=2025-05-28T10:00:00&duration=30

// Response Body
{
  "success": true,
  "message": "Slot is available",
  "data": null
}