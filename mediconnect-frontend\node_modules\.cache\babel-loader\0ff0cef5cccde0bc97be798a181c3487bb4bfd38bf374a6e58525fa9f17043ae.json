{"ast": null, "code": "// API Configuration\nexport const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8083/api';\nexport const USE_MOCK_DATA = process.env.REACT_APP_USE_MOCK_DATA === 'true';\n\n// User Roles\nexport const USER_ROLES = {\n  ADMIN: 'ADMIN',\n  DOCTOR: 'DOCTOR',\n  CLINIC: 'CLINIC',\n  PATIENT: 'PATIENT'\n};\n\n// User Status\nexport const USER_STATUS = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  PENDING_APPROVAL: 'PENDING_APPROVAL',\n  SUSPENDED: 'SUSPENDED'\n};\n\n// Appointment Status\nexport const APPOINTMENT_STATUS = {\n  SCHEDULED: 'SCHEDULED',\n  CONFIRMED: 'CONFIRMED',\n  IN_PROGRESS: 'IN_PROGRESS',\n  COMPLETED: 'COMPLETED',\n  CANCELLED: 'CANCELLED',\n  NO_SHOW: 'NO_SHOW'\n};\n\n// Doctor Status\nexport const DOCTOR_STATUS = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  PENDING_APPROVAL: 'PENDING_APPROVAL',\n  ON_LEAVE: 'ON_LEAVE'\n};\n\n// Clinic Status\nexport const CLINIC_STATUS = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  PENDING_APPROVAL: 'PENDING_APPROVAL',\n  SUSPENDED: 'SUSPENDED'\n};\n\n// Follow-up Status\nexport const FOLLOWUP_STATUS = {\n  SCHEDULED: 'SCHEDULED',\n  COMPLETED: 'COMPLETED',\n  CANCELLED: 'CANCELLED',\n  RESCHEDULED: 'RESCHEDULED'\n};\n\n// Announcement Priority\nexport const ANNOUNCEMENT_PRIORITY = {\n  LOW: 'LOW',\n  MEDIUM: 'MEDIUM',\n  HIGH: 'HIGH',\n  URGENT: 'URGENT'\n};\n\n// Gender Options\nexport const GENDER = {\n  MALE: 'MALE',\n  FEMALE: 'FEMALE',\n  OTHER: 'OTHER'\n};\n\n// Medical Specialties\nexport const MEDICAL_SPECIALTIES = {\n  GENERAL_MEDICINE: 'GENERAL_MEDICINE',\n  CARDIOLOGY: 'CARDIOLOGY',\n  DERMATOLOGY: 'DERMATOLOGY',\n  NEUROLOGY: 'NEUROLOGY',\n  ORTHOPEDICS: 'ORTHOPEDICS',\n  PEDIATRICS: 'PEDIATRICS',\n  PSYCHIATRY: 'PSYCHIATRY',\n  RADIOLOGY: 'RADIOLOGY',\n  SURGERY: 'SURGERY',\n  GYNECOLOGY: 'GYNECOLOGY'\n};\n\n// Blood Groups\nexport const BLOOD_GROUPS = ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'];\n\n// Time Slots (for appointments)\nexport const TIME_SLOTS = ['09:00', '09:30', '10:00', '10:30', '11:00', '11:30', '14:00', '14:30', '15:00', '15:30', '16:00', '16:30', '17:00', '17:30'];\n\n// Navigation Routes\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/login',\n  REGISTER: '/register',\n  DASHBOARD: '/dashboard',\n  PROFILE: '/profile',\n  // Admin Routes\n  ADMIN_DASHBOARD: '/admin/dashboard',\n  ADMIN_USERS: '/admin/users',\n  ADMIN_DOCTORS: '/admin/doctors',\n  ADMIN_CLINICS: '/admin/clinics',\n  ADMIN_REPORTS: '/admin/reports',\n  // Doctor Routes\n  DOCTOR_DASHBOARD: '/doctor/dashboard',\n  DOCTOR_PATIENTS: '/doctor/patients',\n  DOCTOR_APPOINTMENTS: '/doctor/appointments',\n  DOCTOR_SCHEDULE: '/doctor/schedule',\n  // Clinic Routes\n  CLINIC_DASHBOARD: '/clinic/dashboard',\n  CLINIC_STAFF: '/clinic/staff',\n  CLINIC_APPOINTMENTS: '/clinic/appointments',\n  CLINIC_ANNOUNCEMENTS: '/clinic/announcements',\n  // Patient Routes\n  PATIENT_DASHBOARD: '/patient/dashboard',\n  PATIENT_APPOINTMENTS: '/patient/appointments',\n  PATIENT_DOCTORS: '/patient/doctors',\n  PATIENT_HISTORY: '/patient/history'\n};\n\n// Local Storage Keys\nexport const STORAGE_KEYS = {\n  TOKEN: 'mediconnect_token',\n  USER: 'mediconnect_user',\n  THEME: 'mediconnect_theme'\n};\n\n// Pagination\nexport const PAGINATION = {\n  DEFAULT_PAGE_SIZE: 10,\n  PAGE_SIZE_OPTIONS: [5, 10, 20, 50]\n};\n\n// Date Formats\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM dd, yyyy',\n  INPUT: 'yyyy-MM-dd',\n  DATETIME: 'MMM dd, yyyy HH:mm',\n  TIME: 'HH:mm'\n};\n\n// Validation Rules\nexport const VALIDATION = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE_REGEX: /^\\+?[\\d\\s\\-()]+$/,\n  PASSWORD_MIN_LENGTH: 6,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50\n};\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied.',\n  NOT_FOUND: 'Resource not found.',\n  SERVER_ERROR: 'Server error. Please try again later.',\n  VALIDATION_ERROR: 'Please check your input and try again.'\n};\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  LOGIN_SUCCESS: 'Login successful!',\n  LOGOUT_SUCCESS: 'Logout successful!',\n  REGISTRATION_SUCCESS: 'Registration successful!',\n  PROFILE_UPDATED: 'Profile updated successfully!',\n  APPOINTMENT_BOOKED: 'Appointment booked successfully!',\n  APPOINTMENT_CANCELLED: 'Appointment cancelled successfully!'\n};", "map": {"version": 3, "names": ["API_BASE_URL", "process", "env", "REACT_APP_API_BASE_URL", "USE_MOCK_DATA", "REACT_APP_USE_MOCK_DATA", "USER_ROLES", "ADMIN", "DOCTOR", "CLINIC", "PATIENT", "USER_STATUS", "ACTIVE", "INACTIVE", "PENDING_APPROVAL", "SUSPENDED", "APPOINTMENT_STATUS", "SCHEDULED", "CONFIRMED", "IN_PROGRESS", "COMPLETED", "CANCELLED", "NO_SHOW", "DOCTOR_STATUS", "ON_LEAVE", "CLINIC_STATUS", "FOLLOWUP_STATUS", "RESCHEDULED", "ANNOUNCEMENT_PRIORITY", "LOW", "MEDIUM", "HIGH", "URGENT", "GENDER", "MALE", "FEMALE", "OTHER", "MEDICAL_SPECIALTIES", "GENERAL_MEDICINE", "CARDIOLOGY", "DERMATOLOGY", "NEUROLOGY", "ORTHOPEDICS", "PEDIATRICS", "PSYCHIATRY", "RADIOLOGY", "SURGERY", "GYNECOLOGY", "BLOOD_GROUPS", "TIME_SLOTS", "ROUTES", "HOME", "LOGIN", "REGISTER", "DASHBOARD", "PROFILE", "ADMIN_DASHBOARD", "ADMIN_USERS", "ADMIN_DOCTORS", "ADMIN_CLINICS", "ADMIN_REPORTS", "DOCTOR_DASHBOARD", "DOCTOR_PATIENTS", "DOCTOR_APPOINTMENTS", "DOCTOR_SCHEDULE", "CLINIC_DASHBOARD", "CLINIC_STAFF", "CLINIC_APPOINTMENTS", "CLINIC_ANNOUNCEMENTS", "PATIENT_DASHBOARD", "PATIENT_APPOINTMENTS", "PATIENT_DOCTORS", "PATIENT_HISTORY", "STORAGE_KEYS", "TOKEN", "USER", "THEME", "PAGINATION", "DEFAULT_PAGE_SIZE", "PAGE_SIZE_OPTIONS", "DATE_FORMATS", "DISPLAY", "INPUT", "DATETIME", "TIME", "VALIDATION", "EMAIL_REGEX", "PHONE_REGEX", "PASSWORD_MIN_LENGTH", "NAME_MIN_LENGTH", "NAME_MAX_LENGTH", "ERROR_MESSAGES", "NETWORK_ERROR", "UNAUTHORIZED", "FORBIDDEN", "NOT_FOUND", "SERVER_ERROR", "VALIDATION_ERROR", "SUCCESS_MESSAGES", "LOGIN_SUCCESS", "LOGOUT_SUCCESS", "REGISTRATION_SUCCESS", "PROFILE_UPDATED", "APPOINTMENT_BOOKED", "APPOINTMENT_CANCELLED"], "sources": ["C:/Users/<USER>/OneDrive/desktop/MediConnec_Project/mediconnect-frontend/src/utils/constants.js"], "sourcesContent": ["// API Configuration\nexport const API_BASE_URL = process.env.REACT_APP_API_BASE_URL || 'http://localhost:8083/api';\nexport const USE_MOCK_DATA = process.env.REACT_APP_USE_MOCK_DATA === 'true';\n\n// User Roles\nexport const USER_ROLES = {\n  ADMIN: 'ADMIN',\n  DOCTOR: 'DOCTOR',\n  CLINIC: 'CLINIC',\n  PATIENT: 'PATIENT'\n};\n\n// User Status\nexport const USER_STATUS = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  PENDING_APPROVAL: 'PENDING_APPROVAL',\n  SUSPENDED: 'SUSPENDED'\n};\n\n// Appointment Status\nexport const APPOINTMENT_STATUS = {\n  SCHEDULED: 'SCHEDULED',\n  CONFIRMED: 'CONFIRMED',\n  IN_PROGRESS: 'IN_PROGRESS',\n  COMPLETED: 'COMPLETED',\n  CANCELLED: 'CANCELLED',\n  NO_SHOW: 'NO_SHOW'\n};\n\n// Doctor Status\nexport const DOCTOR_STATUS = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  PENDING_APPROVAL: 'PENDING_APPROVAL',\n  ON_LEAVE: 'ON_LEAVE'\n};\n\n// Clinic Status\nexport const CLINIC_STATUS = {\n  ACTIVE: 'ACTIVE',\n  INACTIVE: 'INACTIVE',\n  PENDING_APPROVAL: 'PENDING_APPROVAL',\n  SUSPENDED: 'SUSPENDED'\n};\n\n// Follow-up Status\nexport const FOLLOWUP_STATUS = {\n  SCHEDULED: 'SCHEDULED',\n  COMPLETED: 'COMPLETED',\n  CANCELLED: 'CANCELLED',\n  RESCHEDULED: 'RESCHEDULED'\n};\n\n// Announcement Priority\nexport const ANNOUNCEMENT_PRIORITY = {\n  LOW: 'LOW',\n  MEDIUM: 'MEDIUM',\n  HIGH: 'HIGH',\n  URGENT: 'URGENT'\n};\n\n// Gender Options\nexport const GENDER = {\n  MALE: 'MALE',\n  FEMALE: 'FEMALE',\n  OTHER: 'OTHER'\n};\n\n// Medical Specialties\nexport const MEDICAL_SPECIALTIES = {\n  GENERAL_MEDICINE: 'GENERAL_MEDICINE',\n  CARDIOLOGY: 'CARDIOLOGY',\n  DERMATOLOGY: 'DERMATOLOGY',\n  NEUROLOGY: 'NEUROLOGY',\n  ORTHOPEDICS: 'ORTHOPEDICS',\n  PEDIATRICS: 'PEDIATRICS',\n  PSYCHIATRY: 'PSYCHIATRY',\n  RADIOLOGY: 'RADIOLOGY',\n  SURGERY: 'SURGERY',\n  GYNECOLOGY: 'GYNECOLOGY'\n};\n\n// Blood Groups\nexport const BLOOD_GROUPS = [\n  'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'\n];\n\n// Time Slots (for appointments)\nexport const TIME_SLOTS = [\n  '09:00', '09:30', '10:00', '10:30', '11:00', '11:30',\n  '14:00', '14:30', '15:00', '15:30', '16:00', '16:30',\n  '17:00', '17:30'\n];\n\n// Navigation Routes\nexport const ROUTES = {\n  HOME: '/',\n  LOGIN: '/login',\n  REGISTER: '/register',\n  DASHBOARD: '/dashboard',\n  PROFILE: '/profile',\n\n  // Admin Routes\n  ADMIN_DASHBOARD: '/admin/dashboard',\n  ADMIN_USERS: '/admin/users',\n  ADMIN_DOCTORS: '/admin/doctors',\n  ADMIN_CLINICS: '/admin/clinics',\n  ADMIN_REPORTS: '/admin/reports',\n\n  // Doctor Routes\n  DOCTOR_DASHBOARD: '/doctor/dashboard',\n  DOCTOR_PATIENTS: '/doctor/patients',\n  DOCTOR_APPOINTMENTS: '/doctor/appointments',\n  DOCTOR_SCHEDULE: '/doctor/schedule',\n\n  // Clinic Routes\n  CLINIC_DASHBOARD: '/clinic/dashboard',\n  CLINIC_STAFF: '/clinic/staff',\n  CLINIC_APPOINTMENTS: '/clinic/appointments',\n  CLINIC_ANNOUNCEMENTS: '/clinic/announcements',\n\n  // Patient Routes\n  PATIENT_DASHBOARD: '/patient/dashboard',\n  PATIENT_APPOINTMENTS: '/patient/appointments',\n  PATIENT_DOCTORS: '/patient/doctors',\n  PATIENT_HISTORY: '/patient/history'\n};\n\n// Local Storage Keys\nexport const STORAGE_KEYS = {\n  TOKEN: 'mediconnect_token',\n  USER: 'mediconnect_user',\n  THEME: 'mediconnect_theme'\n};\n\n// Pagination\nexport const PAGINATION = {\n  DEFAULT_PAGE_SIZE: 10,\n  PAGE_SIZE_OPTIONS: [5, 10, 20, 50]\n};\n\n// Date Formats\nexport const DATE_FORMATS = {\n  DISPLAY: 'MMM dd, yyyy',\n  INPUT: 'yyyy-MM-dd',\n  DATETIME: 'MMM dd, yyyy HH:mm',\n  TIME: 'HH:mm'\n};\n\n// Validation Rules\nexport const VALIDATION = {\n  EMAIL_REGEX: /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/,\n  PHONE_REGEX: /^\\+?[\\d\\s\\-()]+$/,\n  PASSWORD_MIN_LENGTH: 6,\n  NAME_MIN_LENGTH: 2,\n  NAME_MAX_LENGTH: 50\n};\n\n// Error Messages\nexport const ERROR_MESSAGES = {\n  NETWORK_ERROR: 'Network error. Please check your connection.',\n  UNAUTHORIZED: 'You are not authorized to perform this action.',\n  FORBIDDEN: 'Access denied.',\n  NOT_FOUND: 'Resource not found.',\n  SERVER_ERROR: 'Server error. Please try again later.',\n  VALIDATION_ERROR: 'Please check your input and try again.'\n};\n\n// Success Messages\nexport const SUCCESS_MESSAGES = {\n  LOGIN_SUCCESS: 'Login successful!',\n  LOGOUT_SUCCESS: 'Logout successful!',\n  REGISTRATION_SUCCESS: 'Registration successful!',\n  PROFILE_UPDATED: 'Profile updated successfully!',\n  APPOINTMENT_BOOKED: 'Appointment booked successfully!',\n  APPOINTMENT_CANCELLED: 'Appointment cancelled successfully!'\n};\n"], "mappings": "AAAA;AACA,OAAO,MAAMA,YAAY,GAAGC,OAAO,CAACC,GAAG,CAACC,sBAAsB,IAAI,2BAA2B;AAC7F,OAAO,MAAMC,aAAa,GAAGH,OAAO,CAACC,GAAG,CAACG,uBAAuB,KAAK,MAAM;;AAE3E;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,QAAQ;EAChBC,MAAM,EAAE,QAAQ;EAChBC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,OAAO,MAAMC,WAAW,GAAG;EACzBC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,gBAAgB,EAAE,kBAAkB;EACpCC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMC,kBAAkB,GAAG;EAChCC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE;AACX,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3BX,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,gBAAgB,EAAE,kBAAkB;EACpCU,QAAQ,EAAE;AACZ,CAAC;;AAED;AACA,OAAO,MAAMC,aAAa,GAAG;EAC3Bb,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,gBAAgB,EAAE,kBAAkB;EACpCC,SAAS,EAAE;AACb,CAAC;;AAED;AACA,OAAO,MAAMW,eAAe,GAAG;EAC7BT,SAAS,EAAE,WAAW;EACtBG,SAAS,EAAE,WAAW;EACtBC,SAAS,EAAE,WAAW;EACtBM,WAAW,EAAE;AACf,CAAC;;AAED;AACA,OAAO,MAAMC,qBAAqB,GAAG;EACnCC,GAAG,EAAE,KAAK;EACVC,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE;AACV,CAAC;;AAED;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,QAAQ;EAChBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,mBAAmB,GAAG;EACjCC,gBAAgB,EAAE,kBAAkB;EACpCC,UAAU,EAAE,YAAY;EACxBC,WAAW,EAAE,aAAa;EAC1BC,SAAS,EAAE,WAAW;EACtBC,WAAW,EAAE,aAAa;EAC1BC,UAAU,EAAE,YAAY;EACxBC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE,WAAW;EACtBC,OAAO,EAAE,SAAS;EAClBC,UAAU,EAAE;AACd,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG,CAC1B,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,IAAI,CACjD;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG,CACxB,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACpD,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,EACpD,OAAO,EAAE,OAAO,CACjB;;AAED;AACA,OAAO,MAAMC,MAAM,GAAG;EACpBC,IAAI,EAAE,GAAG;EACTC,KAAK,EAAE,QAAQ;EACfC,QAAQ,EAAE,WAAW;EACrBC,SAAS,EAAE,YAAY;EACvBC,OAAO,EAAE,UAAU;EAEnB;EACAC,eAAe,EAAE,kBAAkB;EACnCC,WAAW,EAAE,cAAc;EAC3BC,aAAa,EAAE,gBAAgB;EAC/BC,aAAa,EAAE,gBAAgB;EAC/BC,aAAa,EAAE,gBAAgB;EAE/B;EACAC,gBAAgB,EAAE,mBAAmB;EACrCC,eAAe,EAAE,kBAAkB;EACnCC,mBAAmB,EAAE,sBAAsB;EAC3CC,eAAe,EAAE,kBAAkB;EAEnC;EACAC,gBAAgB,EAAE,mBAAmB;EACrCC,YAAY,EAAE,eAAe;EAC7BC,mBAAmB,EAAE,sBAAsB;EAC3CC,oBAAoB,EAAE,uBAAuB;EAE7C;EACAC,iBAAiB,EAAE,oBAAoB;EACvCC,oBAAoB,EAAE,uBAAuB;EAC7CC,eAAe,EAAE,kBAAkB;EACnCC,eAAe,EAAE;AACnB,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,KAAK,EAAE,mBAAmB;EAC1BC,IAAI,EAAE,kBAAkB;EACxBC,KAAK,EAAE;AACT,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,iBAAiB,EAAE,EAAE;EACrBC,iBAAiB,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE;AACnC,CAAC;;AAED;AACA,OAAO,MAAMC,YAAY,GAAG;EAC1BC,OAAO,EAAE,cAAc;EACvBC,KAAK,EAAE,YAAY;EACnBC,QAAQ,EAAE,oBAAoB;EAC9BC,IAAI,EAAE;AACR,CAAC;;AAED;AACA,OAAO,MAAMC,UAAU,GAAG;EACxBC,WAAW,EAAE,4BAA4B;EACzCC,WAAW,EAAE,kBAAkB;EAC/BC,mBAAmB,EAAE,CAAC;EACtBC,eAAe,EAAE,CAAC;EAClBC,eAAe,EAAE;AACnB,CAAC;;AAED;AACA,OAAO,MAAMC,cAAc,GAAG;EAC5BC,aAAa,EAAE,8CAA8C;EAC7DC,YAAY,EAAE,gDAAgD;EAC9DC,SAAS,EAAE,gBAAgB;EAC3BC,SAAS,EAAE,qBAAqB;EAChCC,YAAY,EAAE,uCAAuC;EACrDC,gBAAgB,EAAE;AACpB,CAAC;;AAED;AACA,OAAO,MAAMC,gBAAgB,GAAG;EAC9BC,aAAa,EAAE,mBAAmB;EAClCC,cAAc,EAAE,oBAAoB;EACpCC,oBAAoB,EAAE,0BAA0B;EAChDC,eAAe,EAAE,+BAA+B;EAChDC,kBAAkB,EAAE,kCAAkC;EACtDC,qBAAqB,EAAE;AACzB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}