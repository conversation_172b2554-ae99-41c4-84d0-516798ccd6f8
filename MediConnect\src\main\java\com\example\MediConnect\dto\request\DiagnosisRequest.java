package com.example.MediConnect.dto.request;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DiagnosisRequest {
    
    @NotNull(message = "Patient ID is required")
    private Long patientId;
    
    @NotNull(message = "Doctor ID is required")
    private Long doctorId;
    
    private Long appointmentId;
    
    private String diagnosisCode;
    
    @NotBlank(message = "Diagnosis description is required")
    private String diagnosisDescription;
    
    private String symptoms;
    private String treatmentPlan;
    private String doctorNotes;
    
    private List<PrescriptionRequest> prescriptions;
    
    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class PrescriptionRequest {
        @NotBlank(message = "Medication name is required")
        private String medicationName;
        
        @NotBlank(message = "Dosage is required")
        private String dosage;
        
        @NotBlank(message = "Frequency is required")
        private String frequency;
        
        @NotBlank(message = "Duration is required")
        private String duration;
        
        private String instructions;
        private Integer quantity;
        private Integer refills;
    }
}
