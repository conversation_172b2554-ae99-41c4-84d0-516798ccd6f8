{"ast": null, "code": "import { apiRequest, mockResponse, isMockMode } from './api';\nimport { STORAGE_KEYS } from '../utils/constants';\n\n// Mock authentication data\nconst mockAuthData = {\n  admin: {\n    token: 'mock_admin_token_123',\n    email: '<EMAIL>',\n    role: 'ADMIN',\n    userId: 1,\n    name: 'Test Admin',\n    message: 'Authentication successful'\n  },\n  doctor: {\n    token: 'mock_doctor_token_456',\n    email: '<EMAIL>',\n    role: 'DOCTOR',\n    userId: 2,\n    name: 'Dr. <PERSON>',\n    message: 'Authentication successful'\n  },\n  clinic: {\n    token: 'mock_clinic_token_789',\n    email: '<EMAIL>',\n    role: 'CLINIC',\n    userId: 3,\n    name: 'MediConnect Central Clinic',\n    message: 'Authentication successful'\n  },\n  patient: {\n    token: 'mock_patient_token_101',\n    email: '<EMAIL>',\n    role: 'PATIENT',\n    userId: 4,\n    name: '<PERSON>',\n    message: 'Authentication successful'\n  }\n};\n\n// Authentication Service\nexport const authService = {\n  // Login\n  login: async credentials => {\n    if (isMockMode()) {\n      // Mock login logic\n      const {\n        email,\n        password\n      } = credentials;\n\n      // Simple mock authentication\n      let authData = null;\n      if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.admin;\n      } else if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.doctor;\n      } else if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.clinic;\n      } else if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.patient;\n      }\n      if (authData) {\n        // Store token and user data\n        localStorage.setItem(STORAGE_KEYS.TOKEN, authData.token);\n        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n          id: authData.userId,\n          name: authData.name,\n          email: authData.email,\n          role: authData.role\n        }));\n        return mockResponse(authData);\n      } else {\n        throw new Error('Invalid credentials');\n      }\n    } else {\n      // Real API call\n      const response = await apiRequest.post('/auth/login', credentials);\n\n      // Store token and user data\n      const {\n        token,\n        email,\n        role,\n        userId,\n        name\n      } = response.data;\n      localStorage.setItem(STORAGE_KEYS.TOKEN, token);\n      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n        id: userId,\n        name,\n        email,\n        role\n      }));\n      return response;\n    }\n  },\n  // Register\n  register: async userData => {\n    if (isMockMode()) {\n      // Mock registration\n      const newUser = {\n        ...userData,\n        id: Date.now(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      // Generate mock token based on role\n      const tokenMap = {\n        ADMIN: 'mock_admin_token_new',\n        DOCTOR: 'mock_doctor_token_new',\n        CLINIC: 'mock_clinic_token_new',\n        PATIENT: 'mock_patient_token_new'\n      };\n      const authData = {\n        token: tokenMap[userData.role] || 'mock_token_new',\n        email: userData.email,\n        role: userData.role,\n        userId: newUser.id,\n        name: userData.name,\n        message: 'Registration successful'\n      };\n\n      // Store token and user data\n      localStorage.setItem(STORAGE_KEYS.TOKEN, authData.token);\n      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n        id: authData.userId,\n        name: authData.name,\n        email: authData.email,\n        role: authData.role\n      }));\n      return mockResponse(authData);\n    } else {\n      // Real API call\n      const response = await apiRequest.post('/auth/register', userData);\n\n      // Store token and user data if registration includes auto-login\n      if (response.data.token) {\n        const {\n          token,\n          email,\n          role,\n          userId,\n          name\n        } = response.data;\n        localStorage.setItem(STORAGE_KEYS.TOKEN, token);\n        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n          id: userId,\n          name,\n          email,\n          role\n        }));\n      }\n      return response;\n    }\n  },\n  // Logout\n  logout: async () => {\n    if (isMockMode()) {\n      // Mock logout\n      localStorage.removeItem(STORAGE_KEYS.TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.USER);\n      return mockResponse({\n        success: true,\n        message: 'Logout successful',\n        data: null\n      });\n    } else {\n      // Real API call\n      try {\n        await apiRequest.post('/auth/logout');\n      } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn('Logout API call failed:', error);\n      }\n\n      // Clear local storage\n      localStorage.removeItem(STORAGE_KEYS.TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.USER);\n      return {\n        data: {\n          success: true,\n          message: 'Logout successful'\n        }\n      };\n    }\n  },\n  // Validate token\n  validateToken: async () => {\n    if (isMockMode()) {\n      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n      if (token && token.startsWith('mock_')) {\n        return mockResponse({\n          success: true,\n          message: 'Token is valid',\n          data: null\n        });\n      } else {\n        throw new Error('Invalid token');\n      }\n    } else {\n      return apiRequest.post('/auth/validate');\n    }\n  },\n  // Refresh token\n  refreshToken: async () => {\n    if (isMockMode()) {\n      const currentToken = localStorage.getItem(STORAGE_KEYS.TOKEN);\n      if (currentToken && currentToken.startsWith('mock_')) {\n        const newToken = currentToken + '_refreshed';\n        localStorage.setItem(STORAGE_KEYS.TOKEN, newToken);\n        return mockResponse({\n          success: true,\n          message: 'Token refreshed successfully',\n          data: newToken\n        });\n      } else {\n        throw new Error('No valid token to refresh');\n      }\n    } else {\n      const response = await apiRequest.post('/auth/refresh');\n      localStorage.setItem(STORAGE_KEYS.TOKEN, response.data.data);\n      return response;\n    }\n  },\n  // Get current user\n  getCurrentUser: () => {\n    const userStr = localStorage.getItem(STORAGE_KEYS.USER);\n    return userStr ? JSON.parse(userStr) : null;\n  },\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n    return !!token;\n  },\n  // Get user role\n  getUserRole: () => {\n    const user = authService.getCurrentUser();\n    return (user === null || user === void 0 ? void 0 : user.role) || null;\n  }\n};\nexport default authService;", "map": {"version": 3, "names": ["apiRequest", "mockResponse", "isMockMode", "STORAGE_KEYS", "mockAuthData", "admin", "token", "email", "role", "userId", "name", "message", "doctor", "clinic", "patient", "authService", "login", "credentials", "password", "authData", "localStorage", "setItem", "TOKEN", "USER", "JSON", "stringify", "id", "Error", "response", "post", "data", "register", "userData", "newUser", "Date", "now", "createdAt", "toISOString", "updatedAt", "tokenMap", "ADMIN", "DOCTOR", "CLINIC", "PATIENT", "logout", "removeItem", "success", "error", "console", "warn", "validateToken", "getItem", "startsWith", "refreshToken", "currentToken", "newToken", "getCurrentUser", "userStr", "parse", "isAuthenticated", "getUserRole", "user"], "sources": ["C:/Users/<USER>/OneDrive/desktop/MediConnec_Project/mediconnect-frontend/src/services/authService.js"], "sourcesContent": ["import { apiRequest, mockResponse, isMockMode } from './api';\nimport { STORAGE_KEYS } from '../utils/constants';\n\n// Mock authentication data\nconst mockAuthData = {\n  admin: {\n    token: 'mock_admin_token_123',\n    email: '<EMAIL>',\n    role: 'ADMIN',\n    userId: 1,\n    name: 'Test Admin',\n    message: 'Authentication successful'\n  },\n  doctor: {\n    token: 'mock_doctor_token_456',\n    email: '<EMAIL>',\n    role: 'DOCTOR',\n    userId: 2,\n    name: 'Dr. <PERSON>',\n    message: 'Authentication successful'\n  },\n  clinic: {\n    token: 'mock_clinic_token_789',\n    email: '<EMAIL>',\n    role: 'CLINIC',\n    userId: 3,\n    name: 'MediConnect Central Clinic',\n    message: 'Authentication successful'\n  },\n  patient: {\n    token: 'mock_patient_token_101',\n    email: '<EMAIL>',\n    role: 'PATIENT',\n    userId: 4,\n    name: '<PERSON>',\n    message: 'Authentication successful'\n  }\n};\n\n// Authentication Service\nexport const authService = {\n  // Login\n  login: async (credentials) => {\n    if (isMockMode()) {\n      // Mock login logic\n      const { email, password } = credentials;\n\n      // Simple mock authentication\n      let authData = null;\n      if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.admin;\n      } else if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.doctor;\n      } else if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.clinic;\n      } else if (email === '<EMAIL>' && password === 'test123') {\n        authData = mockAuthData.patient;\n      }\n\n      if (authData) {\n        // Store token and user data\n        localStorage.setItem(STORAGE_KEYS.TOKEN, authData.token);\n        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n          id: authData.userId,\n          name: authData.name,\n          email: authData.email,\n          role: authData.role\n        }));\n\n        return mockResponse(authData);\n      } else {\n        throw new Error('Invalid credentials');\n      }\n    } else {\n      // Real API call\n      const response = await apiRequest.post('/auth/login', credentials);\n\n      // Store token and user data\n      const { token, email, role, userId, name } = response.data;\n      localStorage.setItem(STORAGE_KEYS.TOKEN, token);\n      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n        id: userId,\n        name,\n        email,\n        role\n      }));\n\n      return response;\n    }\n  },\n\n  // Register\n  register: async (userData) => {\n    if (isMockMode()) {\n      // Mock registration\n      const newUser = {\n        ...userData,\n        id: Date.now(),\n        createdAt: new Date().toISOString(),\n        updatedAt: new Date().toISOString()\n      };\n\n      // Generate mock token based on role\n      const tokenMap = {\n        ADMIN: 'mock_admin_token_new',\n        DOCTOR: 'mock_doctor_token_new',\n        CLINIC: 'mock_clinic_token_new',\n        PATIENT: 'mock_patient_token_new'\n      };\n\n      const authData = {\n        token: tokenMap[userData.role] || 'mock_token_new',\n        email: userData.email,\n        role: userData.role,\n        userId: newUser.id,\n        name: userData.name,\n        message: 'Registration successful'\n      };\n\n      // Store token and user data\n      localStorage.setItem(STORAGE_KEYS.TOKEN, authData.token);\n      localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n        id: authData.userId,\n        name: authData.name,\n        email: authData.email,\n        role: authData.role\n      }));\n\n      return mockResponse(authData);\n    } else {\n      // Real API call\n      const response = await apiRequest.post('/auth/register', userData);\n\n      // Store token and user data if registration includes auto-login\n      if (response.data.token) {\n        const { token, email, role, userId, name } = response.data;\n        localStorage.setItem(STORAGE_KEYS.TOKEN, token);\n        localStorage.setItem(STORAGE_KEYS.USER, JSON.stringify({\n          id: userId,\n          name,\n          email,\n          role\n        }));\n      }\n\n      return response;\n    }\n  },\n\n  // Logout\n  logout: async () => {\n    if (isMockMode()) {\n      // Mock logout\n      localStorage.removeItem(STORAGE_KEYS.TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.USER);\n      return mockResponse({\n        success: true,\n        message: 'Logout successful',\n        data: null\n      });\n    } else {\n      // Real API call\n      try {\n        await apiRequest.post('/auth/logout');\n      } catch (error) {\n        // Continue with logout even if API call fails\n        console.warn('Logout API call failed:', error);\n      }\n\n      // Clear local storage\n      localStorage.removeItem(STORAGE_KEYS.TOKEN);\n      localStorage.removeItem(STORAGE_KEYS.USER);\n\n      return { data: { success: true, message: 'Logout successful' } };\n    }\n  },\n\n  // Validate token\n  validateToken: async () => {\n    if (isMockMode()) {\n      const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n      if (token && token.startsWith('mock_')) {\n        return mockResponse({\n          success: true,\n          message: 'Token is valid',\n          data: null\n        });\n      } else {\n        throw new Error('Invalid token');\n      }\n    } else {\n      return apiRequest.post('/auth/validate');\n    }\n  },\n\n  // Refresh token\n  refreshToken: async () => {\n    if (isMockMode()) {\n      const currentToken = localStorage.getItem(STORAGE_KEYS.TOKEN);\n      if (currentToken && currentToken.startsWith('mock_')) {\n        const newToken = currentToken + '_refreshed';\n        localStorage.setItem(STORAGE_KEYS.TOKEN, newToken);\n        return mockResponse({\n          success: true,\n          message: 'Token refreshed successfully',\n          data: newToken\n        });\n      } else {\n        throw new Error('No valid token to refresh');\n      }\n    } else {\n      const response = await apiRequest.post('/auth/refresh');\n      localStorage.setItem(STORAGE_KEYS.TOKEN, response.data.data);\n      return response;\n    }\n  },\n\n  // Get current user\n  getCurrentUser: () => {\n    const userStr = localStorage.getItem(STORAGE_KEYS.USER);\n    return userStr ? JSON.parse(userStr) : null;\n  },\n\n  // Check if user is authenticated\n  isAuthenticated: () => {\n    const token = localStorage.getItem(STORAGE_KEYS.TOKEN);\n    return !!token;\n  },\n\n  // Get user role\n  getUserRole: () => {\n    const user = authService.getCurrentUser();\n    return user?.role || null;\n  }\n};\n\nexport default authService;\n"], "mappings": "AAAA,SAASA,UAAU,EAAEC,YAAY,EAAEC,UAAU,QAAQ,OAAO;AAC5D,SAASC,YAAY,QAAQ,oBAAoB;;AAEjD;AACA,MAAMC,YAAY,GAAG;EACnBC,KAAK,EAAE;IACLC,KAAK,EAAE,sBAAsB;IAC7BC,KAAK,EAAE,oBAAoB;IAC3BC,IAAI,EAAE,OAAO;IACbC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,YAAY;IAClBC,OAAO,EAAE;EACX,CAAC;EACDC,MAAM,EAAE;IACNN,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,+BAA+B;IACtCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,mBAAmB;IACzBC,OAAO,EAAE;EACX,CAAC;EACDE,MAAM,EAAE;IACNP,KAAK,EAAE,uBAAuB;IAC9BC,KAAK,EAAE,yBAAyB;IAChCC,IAAI,EAAE,QAAQ;IACdC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,4BAA4B;IAClCC,OAAO,EAAE;EACX,CAAC;EACDG,OAAO,EAAE;IACPR,KAAK,EAAE,wBAAwB;IAC/BC,KAAK,EAAE,kBAAkB;IACzBC,IAAI,EAAE,SAAS;IACfC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE,cAAc;IACpBC,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMI,WAAW,GAAG;EACzB;EACAC,KAAK,EAAE,MAAOC,WAAW,IAAK;IAC5B,IAAIf,UAAU,CAAC,CAAC,EAAE;MAChB;MACA,MAAM;QAAEK,KAAK;QAAEW;MAAS,CAAC,GAAGD,WAAW;;MAEvC;MACA,IAAIE,QAAQ,GAAG,IAAI;MACnB,IAAIZ,KAAK,KAAK,oBAAoB,IAAIW,QAAQ,KAAK,SAAS,EAAE;QAC5DC,QAAQ,GAAGf,YAAY,CAACC,KAAK;MAC/B,CAAC,MAAM,IAAIE,KAAK,KAAK,+BAA+B,IAAIW,QAAQ,KAAK,SAAS,EAAE;QAC9EC,QAAQ,GAAGf,YAAY,CAACQ,MAAM;MAChC,CAAC,MAAM,IAAIL,KAAK,KAAK,yBAAyB,IAAIW,QAAQ,KAAK,SAAS,EAAE;QACxEC,QAAQ,GAAGf,YAAY,CAACS,MAAM;MAChC,CAAC,MAAM,IAAIN,KAAK,KAAK,kBAAkB,IAAIW,QAAQ,KAAK,SAAS,EAAE;QACjEC,QAAQ,GAAGf,YAAY,CAACU,OAAO;MACjC;MAEA,IAAIK,QAAQ,EAAE;QACZ;QACAC,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,EAAEH,QAAQ,CAACb,KAAK,CAAC;QACxDc,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACoB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACrDC,EAAE,EAAEP,QAAQ,CAACV,MAAM;UACnBC,IAAI,EAAES,QAAQ,CAACT,IAAI;UACnBH,KAAK,EAAEY,QAAQ,CAACZ,KAAK;UACrBC,IAAI,EAAEW,QAAQ,CAACX;QACjB,CAAC,CAAC,CAAC;QAEH,OAAOP,YAAY,CAACkB,QAAQ,CAAC;MAC/B,CAAC,MAAM;QACL,MAAM,IAAIQ,KAAK,CAAC,qBAAqB,CAAC;MACxC;IACF,CAAC,MAAM;MACL;MACA,MAAMC,QAAQ,GAAG,MAAM5B,UAAU,CAAC6B,IAAI,CAAC,aAAa,EAAEZ,WAAW,CAAC;;MAElE;MACA,MAAM;QAAEX,KAAK;QAAEC,KAAK;QAAEC,IAAI;QAAEC,MAAM;QAAEC;MAAK,CAAC,GAAGkB,QAAQ,CAACE,IAAI;MAC1DV,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,EAAEhB,KAAK,CAAC;MAC/Cc,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACoB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACrDC,EAAE,EAAEjB,MAAM;QACVC,IAAI;QACJH,KAAK;QACLC;MACF,CAAC,CAAC,CAAC;MAEH,OAAOoB,QAAQ;IACjB;EACF,CAAC;EAED;EACAG,QAAQ,EAAE,MAAOC,QAAQ,IAAK;IAC5B,IAAI9B,UAAU,CAAC,CAAC,EAAE;MAChB;MACA,MAAM+B,OAAO,GAAG;QACd,GAAGD,QAAQ;QACXN,EAAE,EAAEQ,IAAI,CAACC,GAAG,CAAC,CAAC;QACdC,SAAS,EAAE,IAAIF,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC,CAAC;QACnCC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACG,WAAW,CAAC;MACpC,CAAC;;MAED;MACA,MAAME,QAAQ,GAAG;QACfC,KAAK,EAAE,sBAAsB;QAC7BC,MAAM,EAAE,uBAAuB;QAC/BC,MAAM,EAAE,uBAAuB;QAC/BC,OAAO,EAAE;MACX,CAAC;MAED,MAAMxB,QAAQ,GAAG;QACfb,KAAK,EAAEiC,QAAQ,CAACP,QAAQ,CAACxB,IAAI,CAAC,IAAI,gBAAgB;QAClDD,KAAK,EAAEyB,QAAQ,CAACzB,KAAK;QACrBC,IAAI,EAAEwB,QAAQ,CAACxB,IAAI;QACnBC,MAAM,EAAEwB,OAAO,CAACP,EAAE;QAClBhB,IAAI,EAAEsB,QAAQ,CAACtB,IAAI;QACnBC,OAAO,EAAE;MACX,CAAC;;MAED;MACAS,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,EAAEH,QAAQ,CAACb,KAAK,CAAC;MACxDc,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACoB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;QACrDC,EAAE,EAAEP,QAAQ,CAACV,MAAM;QACnBC,IAAI,EAAES,QAAQ,CAACT,IAAI;QACnBH,KAAK,EAAEY,QAAQ,CAACZ,KAAK;QACrBC,IAAI,EAAEW,QAAQ,CAACX;MACjB,CAAC,CAAC,CAAC;MAEH,OAAOP,YAAY,CAACkB,QAAQ,CAAC;IAC/B,CAAC,MAAM;MACL;MACA,MAAMS,QAAQ,GAAG,MAAM5B,UAAU,CAAC6B,IAAI,CAAC,gBAAgB,EAAEG,QAAQ,CAAC;;MAElE;MACA,IAAIJ,QAAQ,CAACE,IAAI,CAACxB,KAAK,EAAE;QACvB,MAAM;UAAEA,KAAK;UAAEC,KAAK;UAAEC,IAAI;UAAEC,MAAM;UAAEC;QAAK,CAAC,GAAGkB,QAAQ,CAACE,IAAI;QAC1DV,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,EAAEhB,KAAK,CAAC;QAC/Cc,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACoB,IAAI,EAAEC,IAAI,CAACC,SAAS,CAAC;UACrDC,EAAE,EAAEjB,MAAM;UACVC,IAAI;UACJH,KAAK;UACLC;QACF,CAAC,CAAC,CAAC;MACL;MAEA,OAAOoB,QAAQ;IACjB;EACF,CAAC;EAED;EACAgB,MAAM,EAAE,MAAAA,CAAA,KAAY;IAClB,IAAI1C,UAAU,CAAC,CAAC,EAAE;MAChB;MACAkB,YAAY,CAACyB,UAAU,CAAC1C,YAAY,CAACmB,KAAK,CAAC;MAC3CF,YAAY,CAACyB,UAAU,CAAC1C,YAAY,CAACoB,IAAI,CAAC;MAC1C,OAAOtB,YAAY,CAAC;QAClB6C,OAAO,EAAE,IAAI;QACbnC,OAAO,EAAE,mBAAmB;QAC5BmB,IAAI,EAAE;MACR,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,IAAI;QACF,MAAM9B,UAAU,CAAC6B,IAAI,CAAC,cAAc,CAAC;MACvC,CAAC,CAAC,OAAOkB,KAAK,EAAE;QACd;QACAC,OAAO,CAACC,IAAI,CAAC,yBAAyB,EAAEF,KAAK,CAAC;MAChD;;MAEA;MACA3B,YAAY,CAACyB,UAAU,CAAC1C,YAAY,CAACmB,KAAK,CAAC;MAC3CF,YAAY,CAACyB,UAAU,CAAC1C,YAAY,CAACoB,IAAI,CAAC;MAE1C,OAAO;QAAEO,IAAI,EAAE;UAAEgB,OAAO,EAAE,IAAI;UAAEnC,OAAO,EAAE;QAAoB;MAAE,CAAC;IAClE;EACF,CAAC;EAED;EACAuC,aAAa,EAAE,MAAAA,CAAA,KAAY;IACzB,IAAIhD,UAAU,CAAC,CAAC,EAAE;MAChB,MAAMI,KAAK,GAAGc,YAAY,CAAC+B,OAAO,CAAChD,YAAY,CAACmB,KAAK,CAAC;MACtD,IAAIhB,KAAK,IAAIA,KAAK,CAAC8C,UAAU,CAAC,OAAO,CAAC,EAAE;QACtC,OAAOnD,YAAY,CAAC;UAClB6C,OAAO,EAAE,IAAI;UACbnC,OAAO,EAAE,gBAAgB;UACzBmB,IAAI,EAAE;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAIH,KAAK,CAAC,eAAe,CAAC;MAClC;IACF,CAAC,MAAM;MACL,OAAO3B,UAAU,CAAC6B,IAAI,CAAC,gBAAgB,CAAC;IAC1C;EACF,CAAC;EAED;EACAwB,YAAY,EAAE,MAAAA,CAAA,KAAY;IACxB,IAAInD,UAAU,CAAC,CAAC,EAAE;MAChB,MAAMoD,YAAY,GAAGlC,YAAY,CAAC+B,OAAO,CAAChD,YAAY,CAACmB,KAAK,CAAC;MAC7D,IAAIgC,YAAY,IAAIA,YAAY,CAACF,UAAU,CAAC,OAAO,CAAC,EAAE;QACpD,MAAMG,QAAQ,GAAGD,YAAY,GAAG,YAAY;QAC5ClC,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,EAAEiC,QAAQ,CAAC;QAClD,OAAOtD,YAAY,CAAC;UAClB6C,OAAO,EAAE,IAAI;UACbnC,OAAO,EAAE,8BAA8B;UACvCmB,IAAI,EAAEyB;QACR,CAAC,CAAC;MACJ,CAAC,MAAM;QACL,MAAM,IAAI5B,KAAK,CAAC,2BAA2B,CAAC;MAC9C;IACF,CAAC,MAAM;MACL,MAAMC,QAAQ,GAAG,MAAM5B,UAAU,CAAC6B,IAAI,CAAC,eAAe,CAAC;MACvDT,YAAY,CAACC,OAAO,CAAClB,YAAY,CAACmB,KAAK,EAAEM,QAAQ,CAACE,IAAI,CAACA,IAAI,CAAC;MAC5D,OAAOF,QAAQ;IACjB;EACF,CAAC;EAED;EACA4B,cAAc,EAAEA,CAAA,KAAM;IACpB,MAAMC,OAAO,GAAGrC,YAAY,CAAC+B,OAAO,CAAChD,YAAY,CAACoB,IAAI,CAAC;IACvD,OAAOkC,OAAO,GAAGjC,IAAI,CAACkC,KAAK,CAACD,OAAO,CAAC,GAAG,IAAI;EAC7C,CAAC;EAED;EACAE,eAAe,EAAEA,CAAA,KAAM;IACrB,MAAMrD,KAAK,GAAGc,YAAY,CAAC+B,OAAO,CAAChD,YAAY,CAACmB,KAAK,CAAC;IACtD,OAAO,CAAC,CAAChB,KAAK;EAChB,CAAC;EAED;EACAsD,WAAW,EAAEA,CAAA,KAAM;IACjB,MAAMC,IAAI,GAAG9C,WAAW,CAACyC,cAAc,CAAC,CAAC;IACzC,OAAO,CAAAK,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAErD,IAAI,KAAI,IAAI;EAC3B;AACF,CAAC;AAED,eAAeO,WAAW", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}