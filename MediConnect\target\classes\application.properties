server.port=8083
spring.application.name=MediConnect

# Database Configuration
spring.datasource.url=***********************************************************************
spring.datasource.username=root
spring.datasource.password=Hayatu.2020!

# Hibernate Configuration
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# JWT Configuration
jwt.secret=mediconnect_secret_key_2024_very_long_secret_key_for_security
jwt.expiration=86400000

# Logging Configuration
logging.level.com.example.MediConnect=DEBUG
logging.level.org.springframework.security=DEBUG

# Email Configuration (for future use)
spring.mail.host=smtp.gmail.com
spring.mail.port=587
spring.mail.username=<EMAIL>
spring.mail.password=your-app-password
spring.mail.properties.mail.smtp.auth=true
spring.mail.properties.mail.smtp.starttls.enable=true