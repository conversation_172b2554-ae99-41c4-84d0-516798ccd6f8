package com.example.MediConnect.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class DiagnosisDTO {
    private Long id;
    private Long patientId;
    private String patientName;
    private Long doctorId;
    private String doctorName;
    private Long appointmentId;
    private String diagnosisCode;
    private String diagnosisDescription;
    private String symptoms;
    private String treatmentPlan;
    private String doctorNotes;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private List<PrescriptionDTO> prescriptions;
}
