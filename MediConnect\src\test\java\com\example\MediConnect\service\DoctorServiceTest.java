package com.example.MediConnect.service;

import java.util.Optional;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertThrows;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.mockito.junit.jupiter.MockitoExtension;

import com.example.MediConnect.dto.DoctorDTO;
import com.example.MediConnect.dto.PatientDTO;
import com.example.MediConnect.dto.request.DoctorRequest;
import com.example.MediConnect.entity.Doctor;
import com.example.MediConnect.entity.Patient;
import com.example.MediConnect.entity.Speciality;
import com.example.MediConnect.enums.DoctorStatus;
import com.example.MediConnect.enums.Role;
import com.example.MediConnect.enums.Specialization;
import com.example.MediConnect.exception.ResourceNotFoundException;
import com.example.MediConnect.repository.DoctorRepository;
import com.example.MediConnect.repository.PatientRepository;
import com.example.MediConnect.repository.SpecialityRepository;
import com.example.MediConnect.service.impl.DoctorServiceImpl;

@ExtendWith(MockitoExtension.class)
class DoctorServiceTest {

    @Mock
    private DoctorRepository doctorRepository;

    @Mock
    private PatientRepository patientRepository;

    @Mock
    private SpecialityRepository specialityRepository;

    @InjectMocks
    private DoctorServiceImpl doctorService;

    private Doctor testDoctor;
    private Patient testPatient;
    private Speciality testSpeciality;

    @BeforeEach
    void setUp() {
        testSpeciality = new Speciality();
        testSpeciality.setId(1L);
        testSpeciality.setName(Specialization.CARDIOLOGY);
        testSpeciality.setDescription("Heart specialist");

        testDoctor = new Doctor();
        testDoctor.setId(1L);
        testDoctor.setName("Dr. John Smith");
        testDoctor.setEmail("<EMAIL>");
        testDoctor.setPhoneNumber("+**********");
        testDoctor.setAddress("123 Medical St");
        testDoctor.setDateOfBirth("1980-01-01");
        testDoctor.setGender("MALE");
        testDoctor.setRole(Role.DOCTOR);
        testDoctor.setMedicalLicense("MD123456");
        testDoctor.setYearsOfExperience(10);
        testDoctor.setQualification("MBBS, MD");
        testDoctor.setDoctorStatus(DoctorStatus.ACTIVE);
        testDoctor.setConsultationFee(100.0);
        testDoctor.setBio("Experienced cardiologist");
        testDoctor.setSpeciality(testSpeciality);

        testPatient = new Patient();
        testPatient.setId(2L);
        testPatient.setName("Jane Doe");
        testPatient.setEmail("<EMAIL>");
        testPatient.setPhoneNumber("+**********");
        testPatient.setAddress("456 Patient Ave");
        testPatient.setDateOfBirth("1990-01-01");
        testPatient.setGender("FEMALE");
        testPatient.setRole(Role.PATIENT);
        testPatient.setPatientId("PAT001");
        testPatient.setEmergencyContactName("John Doe");
        testPatient.setEmergencyContactPhone("+**********");
        testPatient.setBloodGroup("O+");
    }

    @Test
    void getDoctorProfile_Success() {
        // Given
        when(doctorRepository.findById(1L)).thenReturn(Optional.of(testDoctor));

        // When
        DoctorDTO result = doctorService.getDoctorProfile(1L);

        // Then
        assertNotNull(result);
        assertEquals(testDoctor.getId(), result.getId());
        assertEquals(testDoctor.getName(), result.getName());
        assertEquals(testDoctor.getEmail(), result.getEmail());
        assertEquals(testDoctor.getMedicalLicense(), result.getMedicalLicense());
        assertEquals(testDoctor.getSpeciality().getName().toString(), result.getSpecialtyName());

        verify(doctorRepository, times(1)).findById(1L);
    }

    @Test
    void getDoctorProfile_NotFound() {
        // Given
        when(doctorRepository.findById(1L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            doctorService.getDoctorProfile(1L);
        });

        verify(doctorRepository, times(1)).findById(1L);
    }

    @Test
    void updateDoctorProfile_Success() {
        // Given
        DoctorRequest updateRequest = new DoctorRequest();
        updateRequest.setName("Dr. John Updated");
        updateRequest.setEmail("<EMAIL>");
        updateRequest.setPhoneNumber("+**********");
        updateRequest.setAddress("789 Updated St");
        updateRequest.setDateOfBirth("1980-01-01");
        updateRequest.setGender("MALE");
        updateRequest.setMedicalLicense("MD654321");
        updateRequest.setYearsOfExperience(15);
        updateRequest.setQualification("MBBS, MD, PhD");
        updateRequest.setConsultationFee(150.0);
        updateRequest.setBio("Updated bio");
        updateRequest.setSpecialityId(1L);

        when(doctorRepository.findById(1L)).thenReturn(Optional.of(testDoctor));
        when(specialityRepository.findById(1L)).thenReturn(Optional.of(testSpeciality));
        when(doctorRepository.save(any(Doctor.class))).thenReturn(testDoctor);

        // When
        DoctorDTO result = doctorService.updateDoctorProfile(1L, updateRequest);

        // Then
        assertNotNull(result);
        verify(doctorRepository, times(1)).findById(1L);
        verify(specialityRepository, times(1)).findById(1L);
        verify(doctorRepository, times(1)).save(any(Doctor.class));
    }

    @Test
    void getPatientDetails_Success() {
        // Given
        when(patientRepository.findById(2L)).thenReturn(Optional.of(testPatient));

        // When
        PatientDTO result = doctorService.getPatientDetails(2L);

        // Then
        assertNotNull(result);
        assertEquals(testPatient.getId(), result.getId());
        assertEquals(testPatient.getName(), result.getName());
        assertEquals(testPatient.getEmail(), result.getEmail());
        assertEquals(testPatient.getPatientId(), result.getPatientId());
        assertEquals(testPatient.getBloodGroup(), result.getBloodGroup());

        verify(patientRepository, times(1)).findById(2L);
    }

    @Test
    void getPatientDetails_NotFound() {
        // Given
        when(patientRepository.findById(2L)).thenReturn(Optional.empty());

        // When & Then
        assertThrows(ResourceNotFoundException.class, () -> {
            doctorService.getPatientDetails(2L);
        });

        verify(patientRepository, times(1)).findById(2L);
    }
}
