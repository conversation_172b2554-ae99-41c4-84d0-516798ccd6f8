package com.example.MediConnect.repository;

import com.example.MediConnect.entity.Announcement;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface AnnouncementRepository extends JpaRepository<Announcement, Long> {
    
    List<Announcement> findByClinicId(Long clinicId);
    
    List<Announcement> findByClinicIdAndIsActive(Long clinicId, Boolean isActive);
    
    @Query("SELECT a FROM Announcement a WHERE a.clinic.id = :clinicId AND a.isActive = true AND (a.startDate IS NULL OR a.startDate <= :currentDate) AND (a.endDate IS NULL OR a.endDate >= :currentDate)")
    List<Announcement> findActiveAnnouncementsByClinicId(@Param("clinicId") Long clinicId, @Param("currentDate") LocalDateTime currentDate);
    
    @Query("SELECT a FROM Announcement a WHERE a.clinic.id = :clinicId ORDER BY a.createdAt DESC")
    List<Announcement> findByClinicIdOrderByCreatedAtDesc(@Param("clinicId") Long clinicId);
}
