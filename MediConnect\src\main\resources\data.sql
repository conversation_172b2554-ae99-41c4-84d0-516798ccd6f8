-- Insert sample specialities
INSERT IGNORE INTO specialities (id, name, description, is_active) VALUES
(1, 'GENERAL_MEDICINE', 'General medical practice and primary care', true),
(2, 'CARDIOLOGY', 'Heart and cardiovascular system specialist', true),
(3, 'DERMATOLOGY', 'Skin, hair, and nail specialist', true),
(4, 'PEDIATRICS', 'Medical care for infants, children, and adolescents', true),
(5, 'ORTHOPEDICS', 'Musculoskeletal system specialist', true),
(6, 'NEUROLOGY', 'Nervous system specialist', true),
(7, 'PSYCHIATRY', 'Mental health specialist', true),
(8, 'GYNECOLOGY', 'Women''s reproductive health specialist', true),
(9, 'OPHTHALMOLOGY', 'Eye and vision specialist', true),
(10, 'ENT', 'Ear, nose, and throat specialist', true);

-- Insert sample admin user (password: admin123)
INSERT IGNORE INTO users (id, name, email, password, phone_number, role, status, created_at, updated_at) VALUES
(1, 'System Administrator', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+**********', 'ADMIN', 'ACTIVE', NOW(), NOW());

INSERT IGNORE INTO admins (user_id, employee_id, department, access_level) VALUES
(1, 'ADM001', 'IT Administration', 'SUPER_ADMIN');

-- Insert sample clinic (password: clinic123)
INSERT IGNORE INTO users (id, name, email, password, phone_number, address, role, status, created_at, updated_at) VALUES
(2, 'City Medical Center', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+**********', '123 Main Street, City, State 12345', 'CLINIC', 'ACTIVE', NOW(), NOW());

INSERT IGNORE INTO clinics (user_id, clinic_name, license_number, description, clinic_status, operating_hours, emergency_contact) VALUES
(2, 'City Medical Center', 'LIC001', 'Full-service medical center providing comprehensive healthcare', 'ACTIVE', 'Mon-Fri: 8AM-6PM, Sat: 9AM-2PM', '+**********');

-- Insert sample doctor (password: doctor123)
INSERT IGNORE INTO users (id, name, email, password, phone_number, address, date_of_birth, gender, role, status, created_at, updated_at) VALUES
(3, 'Dr. John Smith', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+**********', '456 Oak Avenue, City, State 12345', '1980-05-15', 'Male', 'DOCTOR', 'ACTIVE', NOW(), NOW());

INSERT IGNORE INTO doctors (user_id, medical_license, speciality_id, years_of_experience, qualification, doctor_status, clinic_id, consultation_fee, bio) VALUES
(3, 'MD12345', 1, 10, 'MD, Internal Medicine', 'ACTIVE', 2, 150.00, 'Experienced general practitioner with focus on preventive care');

-- Insert sample patient (password: patient123)
INSERT IGNORE INTO users (id, name, email, password, phone_number, address, date_of_birth, gender, role, status, created_at, updated_at) VALUES
(4, 'Jane Doe', '<EMAIL>', '$2a$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', '+**********', '789 Pine Street, City, State 12345', '1990-08-22', 'Female', 'PATIENT', 'ACTIVE', NOW(), NOW());

INSERT IGNORE INTO patients (user_id, patient_id, emergency_contact_name, emergency_contact_phone, blood_group, allergies, medical_history, insurance_provider, insurance_number) VALUES
(4, 'PAT001', 'John Doe', '+**********', 'O+', 'None known', 'No significant medical history', 'HealthCare Plus', 'HC123456789');

-- Insert sample doctor schedule
INSERT IGNORE INTO doctor_schedules (doctor_id, day_of_week, start_time, end_time, is_available, slot_duration_minutes) VALUES
(3, 'MONDAY', '09:00:00', '17:00:00', true, 30),
(3, 'TUESDAY', '09:00:00', '17:00:00', true, 30),
(3, 'WEDNESDAY', '09:00:00', '17:00:00', true, 30),
(3, 'THURSDAY', '09:00:00', '17:00:00', true, 30),
(3, 'FRIDAY', '09:00:00', '17:00:00', true, 30);

-- Insert sample appointment
INSERT IGNORE INTO appointments (patient_id, doctor_id, clinic_id, appointment_date, status, reason, duration_minutes, created_at, updated_at) VALUES
(4, 3, 2, '2024-12-20 10:00:00', 'SCHEDULED', 'Regular checkup', 30, NOW(), NOW());

-- Insert sample announcement
INSERT IGNORE INTO announcements (clinic_id, title, content, priority, is_active, start_date, end_date, created_at, updated_at) VALUES
(2, 'Welcome to City Medical Center', 'We are pleased to announce our new online appointment booking system. You can now book appointments 24/7 through our platform.', 'HIGH', true, NOW(), DATE_ADD(NOW(), INTERVAL 30 DAY), NOW(), NOW());
