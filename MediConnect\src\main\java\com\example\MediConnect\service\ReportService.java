package com.example.MediConnect.service;

import com.example.MediConnect.dto.response.SystemReportResponse;

import java.time.LocalDate;

public interface ReportService {
    
    SystemReportResponse generateSystemOverview();
    
    SystemReportResponse generateUserStatistics();
    
    SystemReportResponse generateAppointmentStatistics();
    
    SystemReportResponse generateClinicReport(Long clinicId);
    
    SystemReportResponse generateDoctorReport(Long doctorId);
    
    SystemReportResponse generateDateRangeReport(LocalDate startDate, LocalDate endDate);
}
