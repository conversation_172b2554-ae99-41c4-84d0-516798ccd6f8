{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\OneDrive\\\\desktop\\\\MediConnec_Project\\\\mediconnect-frontend\\\\src\\\\components\\\\admin\\\\AdminDashboard.jsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Users, UserCheck, Building2, Calendar, AlertCircle } from 'lucide-react';\nimport { useAuth } from '../../context/AuthContext';\nimport Card from '../common/Card';\nimport Button from '../common/Button';\nimport { mockAdminStats } from '../../services/mockData';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AdminDashboard = () => {\n  _s();\n  const {\n    user,\n    logout\n  } = useAuth();\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n  useEffect(() => {\n    // Simulate API call to fetch admin statistics\n    const fetchStats = async () => {\n      try {\n        // In real app, this would be an API call\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        setStats(mockAdminStats);\n      } catch (error) {\n        console.error('Failed to fetch stats:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n    fetchStats();\n  }, []);\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen flex items-center justify-center\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 41,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 40,\n      columnNumber: 7\n    }, this);\n  }\n  const statCards = [{\n    title: 'Total Users',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalUsers) || 0,\n    icon: Users,\n    color: 'bg-blue-500',\n    change: '+12%'\n  }, {\n    title: 'Total Doctors',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalDoctors) || 0,\n    icon: UserCheck,\n    color: 'bg-green-500',\n    change: '+8%'\n  }, {\n    title: 'Total Clinics',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalClinics) || 0,\n    icon: Building2,\n    color: 'bg-purple-500',\n    change: '+5%'\n  }, {\n    title: 'Total Appointments',\n    value: (stats === null || stats === void 0 ? void 0 : stats.totalAppointments) || 0,\n    icon: Calendar,\n    color: 'bg-orange-500',\n    change: '+15%'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50\",\n    children: [/*#__PURE__*/_jsxDEV(\"header\", {\n      className: \"bg-white shadow-sm border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex justify-between items-center py-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n              className: \"text-2xl font-bold text-gray-900\",\n              children: \"Admin Dashboard\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 84,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: [\"Welcome back, \", user === null || user === void 0 ? void 0 : user.name]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outline\",\n            onClick: handleLogout,\n            children: \"Logout\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 80,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"main\", {\n      className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\",\n        children: statCards.map((stat, index) => /*#__PURE__*/_jsxDEV(Card, {\n          className: \"relative overflow-hidden\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: `p-3 rounded-lg ${stat.color}`,\n              children: /*#__PURE__*/_jsxDEV(stat.icon, {\n                className: \"h-6 w-6 text-white\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 105,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 104,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"ml-4\",\n              children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-sm font-medium text-gray-600\",\n                children: stat.title\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 108,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-2xl font-bold text-gray-900\",\n                children: stat.value\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 109,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 107,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 103,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"mt-4\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-sm text-green-600 font-medium\",\n              children: [stat.change, \" from last month\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 113,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 112,\n            columnNumber: 15\n          }, this)]\n        }, index, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 100,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\",\n        children: [/*#__PURE__*/_jsxDEV(Card, {\n          title: \"Pending Approvals\",\n          className: \"h-fit\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-yellow-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"h-5 w-5 text-yellow-600 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 127,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Doctor Approvals\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 129,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [(stats === null || stats === void 0 ? void 0 : stats.pendingDoctorApprovals) || 0, \" pending\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 130,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 128,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 126,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"sm\",\n                variant: \"outline\",\n                children: \"Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between p-4 bg-blue-50 rounded-lg\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(AlertCircle, {\n                  className: \"h-5 w-5 text-blue-600 mr-3\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 140,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"font-medium text-gray-900\",\n                    children: \"Clinic Approvals\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 21\n                  }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                    className: \"text-sm text-gray-600\",\n                    children: [(stats === null || stats === void 0 ? void 0 : stats.pendingClinicApprovals) || 0, \" pending\"]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 143,\n                    columnNumber: 21\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 139,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(Button, {\n                size: \"sm\",\n                variant: \"outline\",\n                children: \"Review\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 146,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 124,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 123,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Card, {\n          title: \"Recent Activity\",\n          className: \"h-fit\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"space-y-4\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between py-3 border-b border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-900\",\n                  children: \"New doctor registration\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 157,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"Dr. Sarah Johnson\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: \"2 hours ago\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 160,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 155,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between py-3 border-b border-gray-100\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-900\",\n                  children: \"Clinic approved\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 165,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"MediConnect Central\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 166,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 164,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: \"4 hours ago\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 168,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 163,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"flex items-center justify-between py-3\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"font-medium text-gray-900\",\n                  children: \"System backup completed\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 173,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm text-gray-600\",\n                  children: \"All data secured\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 174,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 172,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-xs text-gray-500\",\n                children: \"6 hours ago\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 176,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 154,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 153,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Card, {\n        title: \"System Overview\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-green-600 mb-2\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.completedAppointments) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 186,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Completed Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 189,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 185,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-blue-600 mb-2\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.todayAppointments) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 193,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Today's Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 196,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 192,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"text-3xl font-bold text-red-600 mb-2\",\n              children: (stats === null || stats === void 0 ? void 0 : stats.cancelledAppointments) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 200,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm text-gray-600\",\n              children: \"Cancelled Appointments\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 203,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 199,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 184,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 183,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 98,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 78,\n    columnNumber: 5\n  }, this);\n};\n_s(AdminDashboard, \"ycNpw0r4WiaWb68f/wt+lQ9ESk8=\", false, function () {\n  return [useAuth];\n});\n_c = AdminDashboard;\nexport default AdminDashboard;\nvar _c;\n$RefreshReg$(_c, \"AdminDashboard\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Users", "UserCheck", "Building2", "Calendar", "AlertCircle", "useAuth", "Card", "<PERSON><PERSON>", "mockAdminStats", "jsxDEV", "_jsxDEV", "AdminDashboard", "_s", "user", "logout", "stats", "setStats", "loading", "setLoading", "fetchStats", "Promise", "resolve", "setTimeout", "error", "console", "handleLogout", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "statCards", "title", "value", "totalUsers", "icon", "color", "change", "totalDoctors", "totalClinics", "totalAppointments", "name", "variant", "onClick", "map", "stat", "index", "pendingDoctorApprovals", "size", "pendingClinicApprovals", "completedAppointments", "todayAppointments", "cancelledAppointments", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/OneDrive/desktop/MediConnec_Project/mediconnect-frontend/src/components/admin/AdminDashboard.jsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport { Users, UserCheck, Building2, Calendar, AlertCircle } from 'lucide-react';\nimport { useAuth } from '../../context/AuthContext';\nimport Card from '../common/Card';\nimport Button from '../common/Button';\nimport { mockAdminStats } from '../../services/mockData';\n\nconst AdminDashboard = () => {\n  const { user, logout } = useAuth();\n  const [stats, setStats] = useState(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    // Simulate API call to fetch admin statistics\n    const fetchStats = async () => {\n      try {\n        // In real app, this would be an API call\n        await new Promise(resolve => setTimeout(resolve, 1000));\n        setStats(mockAdminStats);\n      } catch (error) {\n        console.error('Failed to fetch stats:', error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchStats();\n  }, []);\n\n  const handleLogout = async () => {\n    try {\n      await logout();\n    } catch (error) {\n      console.error('Logout failed:', error);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"min-h-screen flex items-center justify-center\">\n        <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary-600\"></div>\n      </div>\n    );\n  }\n\n  const statCards = [\n    {\n      title: 'Total Users',\n      value: stats?.totalUsers || 0,\n      icon: Users,\n      color: 'bg-blue-500',\n      change: '+12%'\n    },\n    {\n      title: 'Total Doctors',\n      value: stats?.totalDoctors || 0,\n      icon: UserCheck,\n      color: 'bg-green-500',\n      change: '+8%'\n    },\n    {\n      title: 'Total Clinics',\n      value: stats?.totalClinics || 0,\n      icon: Building2,\n      color: 'bg-purple-500',\n      change: '+5%'\n    },\n    {\n      title: 'Total Appointments',\n      value: stats?.totalAppointments || 0,\n      icon: Calendar,\n      color: 'bg-orange-500',\n      change: '+15%'\n    }\n  ];\n\n  return (\n    <div className=\"min-h-screen bg-gray-50\">\n      {/* Header */}\n      <header className=\"bg-white shadow-sm border-b border-gray-200\">\n        <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n          <div className=\"flex justify-between items-center py-4\">\n            <div>\n              <h1 className=\"text-2xl font-bold text-gray-900\">Admin Dashboard</h1>\n              <p className=\"text-sm text-gray-600\">Welcome back, {user?.name}</p>\n            </div>\n            <Button\n              variant=\"outline\"\n              onClick={handleLogout}\n            >\n              Logout\n            </Button>\n          </div>\n        </div>\n      </header>\n\n      {/* Main Content */}\n      <main className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\">\n        {/* Statistics Cards */}\n        <div className=\"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8\">\n          {statCards.map((stat, index) => (\n            <Card key={index} className=\"relative overflow-hidden\">\n              <div className=\"flex items-center\">\n                <div className={`p-3 rounded-lg ${stat.color}`}>\n                  <stat.icon className=\"h-6 w-6 text-white\" />\n                </div>\n                <div className=\"ml-4\">\n                  <p className=\"text-sm font-medium text-gray-600\">{stat.title}</p>\n                  <p className=\"text-2xl font-bold text-gray-900\">{stat.value}</p>\n                </div>\n              </div>\n              <div className=\"mt-4\">\n                <span className=\"text-sm text-green-600 font-medium\">\n                  {stat.change} from last month\n                </span>\n              </div>\n            </Card>\n          ))}\n        </div>\n\n        {/* Quick Actions */}\n        <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8\">\n          <Card title=\"Pending Approvals\" className=\"h-fit\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between p-4 bg-yellow-50 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <AlertCircle className=\"h-5 w-5 text-yellow-600 mr-3\" />\n                  <div>\n                    <p className=\"font-medium text-gray-900\">Doctor Approvals</p>\n                    <p className=\"text-sm text-gray-600\">{stats?.pendingDoctorApprovals || 0} pending</p>\n                  </div>\n                </div>\n                <Button size=\"sm\" variant=\"outline\">\n                  Review\n                </Button>\n              </div>\n\n              <div className=\"flex items-center justify-between p-4 bg-blue-50 rounded-lg\">\n                <div className=\"flex items-center\">\n                  <AlertCircle className=\"h-5 w-5 text-blue-600 mr-3\" />\n                  <div>\n                    <p className=\"font-medium text-gray-900\">Clinic Approvals</p>\n                    <p className=\"text-sm text-gray-600\">{stats?.pendingClinicApprovals || 0} pending</p>\n                  </div>\n                </div>\n                <Button size=\"sm\" variant=\"outline\">\n                  Review\n                </Button>\n              </div>\n            </div>\n          </Card>\n\n          <Card title=\"Recent Activity\" className=\"h-fit\">\n            <div className=\"space-y-4\">\n              <div className=\"flex items-center justify-between py-3 border-b border-gray-100\">\n                <div>\n                  <p className=\"font-medium text-gray-900\">New doctor registration</p>\n                  <p className=\"text-sm text-gray-600\">Dr. Sarah Johnson</p>\n                </div>\n                <span className=\"text-xs text-gray-500\">2 hours ago</span>\n              </div>\n\n              <div className=\"flex items-center justify-between py-3 border-b border-gray-100\">\n                <div>\n                  <p className=\"font-medium text-gray-900\">Clinic approved</p>\n                  <p className=\"text-sm text-gray-600\">MediConnect Central</p>\n                </div>\n                <span className=\"text-xs text-gray-500\">4 hours ago</span>\n              </div>\n\n              <div className=\"flex items-center justify-between py-3\">\n                <div>\n                  <p className=\"font-medium text-gray-900\">System backup completed</p>\n                  <p className=\"text-sm text-gray-600\">All data secured</p>\n                </div>\n                <span className=\"text-xs text-gray-500\">6 hours ago</span>\n              </div>\n            </div>\n          </Card>\n        </div>\n\n        {/* System Overview */}\n        <Card title=\"System Overview\">\n          <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-green-600 mb-2\">\n                {stats?.completedAppointments || 0}\n              </div>\n              <p className=\"text-sm text-gray-600\">Completed Appointments</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-blue-600 mb-2\">\n                {stats?.todayAppointments || 0}\n              </div>\n              <p className=\"text-sm text-gray-600\">Today's Appointments</p>\n            </div>\n\n            <div className=\"text-center\">\n              <div className=\"text-3xl font-bold text-red-600 mb-2\">\n                {stats?.cancelledAppointments || 0}\n              </div>\n              <p className=\"text-sm text-gray-600\">Cancelled Appointments</p>\n            </div>\n          </div>\n        </Card>\n      </main>\n    </div>\n  );\n};\n\nexport default AdminDashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SAASC,KAAK,EAAEC,SAAS,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,QAAQ,cAAc;AACjF,SAASC,OAAO,QAAQ,2BAA2B;AACnD,OAAOC,IAAI,MAAM,gBAAgB;AACjC,OAAOC,MAAM,MAAM,kBAAkB;AACrC,SAASC,cAAc,QAAQ,yBAAyB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,cAAc,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC3B,MAAM;IAAEC,IAAI;IAAEC;EAAO,CAAC,GAAGT,OAAO,CAAC,CAAC;EAClC,MAAM,CAACU,KAAK,EAAEC,QAAQ,CAAC,GAAGlB,QAAQ,CAAC,IAAI,CAAC;EACxC,MAAM,CAACmB,OAAO,EAAEC,UAAU,CAAC,GAAGpB,QAAQ,CAAC,IAAI,CAAC;EAE5CC,SAAS,CAAC,MAAM;IACd;IACA,MAAMoB,UAAU,GAAG,MAAAA,CAAA,KAAY;MAC7B,IAAI;QACF;QACA,MAAM,IAAIC,OAAO,CAACC,OAAO,IAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC,CAAC;QACvDL,QAAQ,CAACR,cAAc,CAAC;MAC1B,CAAC,CAAC,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAChD,CAAC,SAAS;QACRL,UAAU,CAAC,KAAK,CAAC;MACnB;IACF,CAAC;IAEDC,UAAU,CAAC,CAAC;EACd,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMM,YAAY,GAAG,MAAAA,CAAA,KAAY;IAC/B,IAAI;MACF,MAAMX,MAAM,CAAC,CAAC;IAChB,CAAC,CAAC,OAAOS,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,gBAAgB,EAAEA,KAAK,CAAC;IACxC;EACF,CAAC;EAED,IAAIN,OAAO,EAAE;IACX,oBACEP,OAAA;MAAKgB,SAAS,EAAC,+CAA+C;MAAAC,QAAA,eAC5DjB,OAAA;QAAKgB,SAAS,EAAC;MAAmE;QAAAE,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACtF,CAAC;EAEV;EAEA,MAAMC,SAAS,GAAG,CAChB;IACEC,KAAK,EAAE,aAAa;IACpBC,KAAK,EAAE,CAAAnB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoB,UAAU,KAAI,CAAC;IAC7BC,IAAI,EAAEpC,KAAK;IACXqC,KAAK,EAAE,aAAa;IACpBC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAAnB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEwB,YAAY,KAAI,CAAC;IAC/BH,IAAI,EAAEnC,SAAS;IACfoC,KAAK,EAAE,cAAc;IACrBC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,CAAAnB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEyB,YAAY,KAAI,CAAC;IAC/BJ,IAAI,EAAElC,SAAS;IACfmC,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE;EACV,CAAC,EACD;IACEL,KAAK,EAAE,oBAAoB;IAC3BC,KAAK,EAAE,CAAAnB,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAE0B,iBAAiB,KAAI,CAAC;IACpCL,IAAI,EAAEjC,QAAQ;IACdkC,KAAK,EAAE,eAAe;IACtBC,MAAM,EAAE;EACV,CAAC,CACF;EAED,oBACE5B,OAAA;IAAKgB,SAAS,EAAC,yBAAyB;IAAAC,QAAA,gBAEtCjB,OAAA;MAAQgB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,eAC7DjB,OAAA;QAAKgB,SAAS,EAAC,wCAAwC;QAAAC,QAAA,eACrDjB,OAAA;UAAKgB,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDjB,OAAA;YAAAiB,QAAA,gBACEjB,OAAA;cAAIgB,SAAS,EAAC,kCAAkC;cAAAC,QAAA,EAAC;YAAe;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACrErB,OAAA;cAAGgB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,GAAC,gBAAc,EAACd,IAAI,aAAJA,IAAI,uBAAJA,IAAI,CAAE6B,IAAI;YAAA;cAAAd,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChE,CAAC,eACNrB,OAAA,CAACH,MAAM;YACLoC,OAAO,EAAC,SAAS;YACjBC,OAAO,EAAEnB,YAAa;YAAAE,QAAA,EACvB;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACA,CAAC,eAGTrB,OAAA;MAAMgB,SAAS,EAAC,6CAA6C;MAAAC,QAAA,gBAE3DjB,OAAA;QAAKgB,SAAS,EAAC,2DAA2D;QAAAC,QAAA,EACvEK,SAAS,CAACa,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBACzBrC,OAAA,CAACJ,IAAI;UAAaoB,SAAS,EAAC,0BAA0B;UAAAC,QAAA,gBACpDjB,OAAA;YAAKgB,SAAS,EAAC,mBAAmB;YAAAC,QAAA,gBAChCjB,OAAA;cAAKgB,SAAS,EAAE,kBAAkBoB,IAAI,CAACT,KAAK,EAAG;cAAAV,QAAA,eAC7CjB,OAAA,CAACoC,IAAI,CAACV,IAAI;gBAACV,SAAS,EAAC;cAAoB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzC,CAAC,eACNrB,OAAA;cAAKgB,SAAS,EAAC,MAAM;cAAAC,QAAA,gBACnBjB,OAAA;gBAAGgB,SAAS,EAAC,mCAAmC;gBAAAC,QAAA,EAAEmB,IAAI,CAACb;cAAK;gBAAAL,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eACjErB,OAAA;gBAAGgB,SAAS,EAAC,kCAAkC;gBAAAC,QAAA,EAAEmB,IAAI,CAACZ;cAAK;gBAAAN,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNrB,OAAA;YAAKgB,SAAS,EAAC,MAAM;YAAAC,QAAA,eACnBjB,OAAA;cAAMgB,SAAS,EAAC,oCAAoC;cAAAC,QAAA,GACjDmB,IAAI,CAACR,MAAM,EAAC,kBACf;YAAA;cAAAV,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACJ,CAAC;QAAA,GAdGgB,KAAK;UAAAnB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAeV,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNrB,OAAA;QAAKgB,SAAS,EAAC,4CAA4C;QAAAC,QAAA,gBACzDjB,OAAA,CAACJ,IAAI;UAAC2B,KAAK,EAAC,mBAAmB;UAACP,SAAS,EAAC,OAAO;UAAAC,QAAA,eAC/CjB,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjB,OAAA;cAAKgB,SAAS,EAAC,+DAA+D;cAAAC,QAAA,gBAC5EjB,OAAA;gBAAKgB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjB,OAAA,CAACN,WAAW;kBAACsB,SAAS,EAAC;gBAA8B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACxDrB,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAGgB,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7DrB,OAAA;oBAAGgB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAE,CAAAZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEiC,sBAAsB,KAAI,CAAC,EAAC,UAAQ;kBAAA;oBAAApB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrB,OAAA,CAACH,MAAM;gBAAC0C,IAAI,EAAC,IAAI;gBAACN,OAAO,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAEpC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAENrB,OAAA;cAAKgB,SAAS,EAAC,6DAA6D;cAAAC,QAAA,gBAC1EjB,OAAA;gBAAKgB,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCjB,OAAA,CAACN,WAAW;kBAACsB,SAAS,EAAC;gBAA4B;kBAAAE,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE,CAAC,eACtDrB,OAAA;kBAAAiB,QAAA,gBACEjB,OAAA;oBAAGgB,SAAS,EAAC,2BAA2B;oBAAAC,QAAA,EAAC;kBAAgB;oBAAAC,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC,eAC7DrB,OAAA;oBAAGgB,SAAS,EAAC,uBAAuB;oBAAAC,QAAA,GAAE,CAAAZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEmC,sBAAsB,KAAI,CAAC,EAAC,UAAQ;kBAAA;oBAAAtB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAG,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClF,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNrB,OAAA,CAACH,MAAM;gBAAC0C,IAAI,EAAC,IAAI;gBAACN,OAAO,EAAC,SAAS;gBAAAhB,QAAA,EAAC;cAEpC;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPrB,OAAA,CAACJ,IAAI;UAAC2B,KAAK,EAAC,iBAAiB;UAACP,SAAS,EAAC,OAAO;UAAAC,QAAA,eAC7CjB,OAAA;YAAKgB,SAAS,EAAC,WAAW;YAAAC,QAAA,gBACxBjB,OAAA;cAAKgB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EjB,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAGgB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpErB,OAAA;kBAAGgB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAiB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACvD,CAAC,eACNrB,OAAA;gBAAMgB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAENrB,OAAA;cAAKgB,SAAS,EAAC,iEAAiE;cAAAC,QAAA,gBAC9EjB,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAGgB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAe;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eAC5DrB,OAAA;kBAAGgB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAmB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACzD,CAAC,eACNrB,OAAA;gBAAMgB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC,eAENrB,OAAA;cAAKgB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,gBACrDjB,OAAA;gBAAAiB,QAAA,gBACEjB,OAAA;kBAAGgB,SAAS,EAAC,2BAA2B;kBAAAC,QAAA,EAAC;gBAAuB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC,eACpErB,OAAA;kBAAGgB,SAAS,EAAC,uBAAuB;kBAAAC,QAAA,EAAC;gBAAgB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACtD,CAAC,eACNrB,OAAA;gBAAMgB,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACJ,CAAC,eAGNrB,OAAA,CAACJ,IAAI;QAAC2B,KAAK,EAAC,iBAAiB;QAAAN,QAAA,eAC3BjB,OAAA;UAAKgB,SAAS,EAAC,uCAAuC;UAAAC,QAAA,gBACpDjB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA;cAAKgB,SAAS,EAAC,wCAAwC;cAAAC,QAAA,EACpD,CAAAZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEoC,qBAAqB,KAAI;YAAC;cAAAvB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACNrB,OAAA;cAAGgB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC,eAENrB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA;cAAKgB,SAAS,EAAC,uCAAuC;cAAAC,QAAA,EACnD,CAAAZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEqC,iBAAiB,KAAI;YAAC;cAAAxB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B,CAAC,eACNrB,OAAA;cAAGgB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAoB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1D,CAAC,eAENrB,OAAA;YAAKgB,SAAS,EAAC,aAAa;YAAAC,QAAA,gBAC1BjB,OAAA;cAAKgB,SAAS,EAAC,sCAAsC;cAAAC,QAAA,EAClD,CAAAZ,KAAK,aAALA,KAAK,uBAALA,KAAK,CAAEsC,qBAAqB,KAAI;YAAC;cAAAzB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/B,CAAC,eACNrB,OAAA;cAAGgB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAsB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC5D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACJ,CAAC;AAEV,CAAC;AAACnB,EAAA,CA1MID,cAAc;EAAA,QACON,OAAO;AAAA;AAAAiD,EAAA,GAD5B3C,cAAc;AA4MpB,eAAeA,cAAc;AAAC,IAAA2C,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}