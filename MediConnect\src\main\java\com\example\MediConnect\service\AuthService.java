package com.example.MediConnect.service;

import com.example.MediConnect.dto.request.LoginRequest;
import com.example.MediConnect.dto.request.RegisterRequest;
import com.example.MediConnect.dto.response.AuthResponse;

public interface AuthService {
    
    AuthResponse login(LoginRequest loginRequest);
    
    AuthResponse register(RegisterRequest registerRequest);
    
    void logout(String token);
    
    boolean validateToken(String token);
    
    String refreshToken(String token);
}
