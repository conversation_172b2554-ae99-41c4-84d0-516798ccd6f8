package com.example.MediConnect.repository;

import com.example.MediConnect.entity.FollowUp;
import com.example.MediConnect.enums.FollowUpStatus;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

@Repository
public interface FollowUpRepository extends JpaRepository<FollowUp, Long> {
    
    List<FollowUp> findByPatientId(Long patientId);
    
    List<FollowUp> findByDoctorId(Long doctorId);
    
    List<FollowUp> findByStatus(FollowUpStatus status);
    
    @Query("SELECT f FROM FollowUp f WHERE f.patient.id = :patientId AND f.status = :status")
    List<FollowUp> findByPatientIdAndStatus(@Param("patientId") Long patientId, @Param("status") FollowUpStatus status);
    
    @Query("SELECT f FROM FollowUp f WHERE f.doctor.id = :doctorId AND f.status = :status")
    List<FollowUp> findByDoctorIdAndStatus(@Param("doctorId") Long doctorId, @Param("status") FollowUpStatus status);
    
    @Query("SELECT f FROM FollowUp f WHERE f.scheduledDate BETWEEN :startDate AND :endDate")
    List<FollowUp> findByScheduledDateBetween(@Param("startDate") LocalDateTime startDate, @Param("endDate") LocalDateTime endDate);
    
    @Query("SELECT f FROM FollowUp f WHERE f.doctor.id = :doctorId ORDER BY f.scheduledDate ASC")
    List<FollowUp> findByDoctorIdOrderByScheduledDateAsc(@Param("doctorId") Long doctorId);
}
